INFO 2025-06-13 09:06:09,541 autoreload 1697333 128116913432384 Watching for file changes with StatReloader
INFO 2025-06-13 09:07:14,981 autoreload 1699457 126956637026112 Watching for file changes with StatReloader
INFO 2025-06-13 09:13:19,808 basehttp 1699457 126956055033536 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-06-13 09:13:20,374 basehttp 1699457 126956055033536 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4173
INFO 2025-06-13 09:13:26,011 basehttp 1699457 126956027758272 "GET /static/admin/css/login.css HTTP/1.1" 200 951
INFO 2025-06-13 09:13:26,016 basehttp 1699457 126956036150976 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
INFO 2025-06-13 09:13:26,017 basehttp 1699457 126956055033536 "GET /static/admin/css/base.css HTTP/1.1" 200 22120
INFO 2025-06-13 09:13:26,020 basehttp 1699457 126956046640832 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
INFO 2025-06-13 09:13:26,024 basehttp 1699457 126955810715328 "GET /static/admin/css/responsive.css HTTP/1.1" 200 16565
INFO 2025-06-13 09:13:26,043 basehttp 1699457 126955802322624 "GET /static/admin/js/theme.js HTTP/1.1" 200 1653
INFO 2025-06-13 09:13:26,054 basehttp 1699457 126955810715328 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
WARNING 2025-06-13 09:13:26,388 log 1699457 126955810715328 Not Found: /favicon.ico
WARNING 2025-06-13 09:13:26,388 basehttp 1699457 126955810715328 "GET /favicon.ico HTTP/1.1" 404 3174
WARNING 2025-06-13 09:15:02,754 log 1699457 126955793929920 Unauthorized: /api/documents/documents/
WARNING 2025-06-13 09:15:02,756 basehttp 1699457 126955793929920 "GET /api/documents/documents/ HTTP/1.1" 401 58
WARNING 2025-06-13 09:33:57,134 log 1699457 126955793929920 Unauthorized: /api/users/login/
WARNING 2025-06-13 09:33:57,135 basehttp 1699457 126955793929920 "POST /api/users/login/ HTTP/1.1" 401 31
INFO 2025-06-13 09:34:30,856 basehttp 1699457 126955793929920 "POST /api/users/login/ HTTP/1.1" 200 190
ERROR 2025-06-13 09:34:31,056 log 1699457 126955793929920 Internal Server Error: /api/documents/documents/
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: documents_document

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/pagination.py", line 211, in paginate_queryset
    self.page = paginator.page(page_number)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 89, in page
    number = self.validate_number(number)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 70, in validate_number
    if number > self.num_pages:
                ^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 116, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
       ^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 110, in count
    return c()
           ^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/query.py", line 604, in count
    return self.query.get_count(using=self.db)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 644, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 626, in get_aggregation
    result = compiler.execute_sql(SINGLE)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: documents_document
ERROR 2025-06-13 09:34:31,062 basehttp 1699457 126955793929920 "GET /api/documents/documents/ HTTP/1.1" 500 209788
ERROR 2025-06-13 09:34:32,102 log 1699457 126955793929920 Internal Server Error: /api/documents/documents/
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: documents_document

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/pagination.py", line 211, in paginate_queryset
    self.page = paginator.page(page_number)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 89, in page
    number = self.validate_number(number)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 70, in validate_number
    if number > self.num_pages:
                ^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 116, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
       ^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 110, in count
    return c()
           ^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/query.py", line 604, in count
    return self.query.get_count(using=self.db)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 644, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 626, in get_aggregation
    result = compiler.execute_sql(SINGLE)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: documents_document
ERROR 2025-06-13 09:34:32,104 basehttp 1699457 126955793929920 "GET /api/documents/documents/ HTTP/1.1" 500 209788
ERROR 2025-06-13 09:34:37,143 log 1699457 126955793929920 Internal Server Error: /api/documents/documents/
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: documents_document

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/pagination.py", line 211, in paginate_queryset
    self.page = paginator.page(page_number)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 89, in page
    number = self.validate_number(number)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 70, in validate_number
    if number > self.num_pages:
                ^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 116, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
       ^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 110, in count
    return c()
           ^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/query.py", line 604, in count
    return self.query.get_count(using=self.db)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 644, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 626, in get_aggregation
    result = compiler.execute_sql(SINGLE)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: documents_document
ERROR 2025-06-13 09:34:37,144 basehttp 1699457 126955793929920 "GET /api/documents/documents/ HTTP/1.1" 500 209788
ERROR 2025-06-13 09:34:38,182 log 1699457 126955793929920 Internal Server Error: /api/documents/documents/
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: documents_document

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/pagination.py", line 211, in paginate_queryset
    self.page = paginator.page(page_number)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 89, in page
    number = self.validate_number(number)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 70, in validate_number
    if number > self.num_pages:
                ^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 116, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
       ^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 110, in count
    return c()
           ^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/query.py", line 604, in count
    return self.query.get_count(using=self.db)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 644, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 626, in get_aggregation
    result = compiler.execute_sql(SINGLE)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: documents_document
ERROR 2025-06-13 09:34:38,183 basehttp 1699457 126955793929920 "GET /api/documents/documents/ HTTP/1.1" 500 209788
ERROR 2025-06-13 09:34:43,214 log 1699457 126955793929920 Internal Server Error: /api/documents/documents/
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: documents_document

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/pagination.py", line 211, in paginate_queryset
    self.page = paginator.page(page_number)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 89, in page
    number = self.validate_number(number)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 70, in validate_number
    if number > self.num_pages:
                ^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 116, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
       ^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 110, in count
    return c()
           ^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/query.py", line 604, in count
    return self.query.get_count(using=self.db)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 644, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 626, in get_aggregation
    result = compiler.execute_sql(SINGLE)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: documents_document
ERROR 2025-06-13 09:34:43,216 basehttp 1699457 126955793929920 "GET /api/documents/documents/ HTTP/1.1" 500 209788
ERROR 2025-06-13 09:34:44,258 log 1699457 126955793929920 Internal Server Error: /api/documents/documents/
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: documents_document

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/pagination.py", line 211, in paginate_queryset
    self.page = paginator.page(page_number)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 89, in page
    number = self.validate_number(number)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 70, in validate_number
    if number > self.num_pages:
                ^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 116, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
       ^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 110, in count
    return c()
           ^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/query.py", line 604, in count
    return self.query.get_count(using=self.db)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 644, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 626, in get_aggregation
    result = compiler.execute_sql(SINGLE)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: documents_document
ERROR 2025-06-13 09:34:44,261 basehttp 1699457 126955793929920 "GET /api/documents/documents/ HTTP/1.1" 500 209788
ERROR 2025-06-13 09:34:49,291 log 1699457 126955793929920 Internal Server Error: /api/documents/documents/
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: documents_document

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/pagination.py", line 211, in paginate_queryset
    self.page = paginator.page(page_number)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 89, in page
    number = self.validate_number(number)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 70, in validate_number
    if number > self.num_pages:
                ^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 116, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
       ^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 110, in count
    return c()
           ^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/query.py", line 604, in count
    return self.query.get_count(using=self.db)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 644, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 626, in get_aggregation
    result = compiler.execute_sql(SINGLE)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: documents_document
ERROR 2025-06-13 09:34:49,292 basehttp 1699457 126955793929920 "GET /api/documents/documents/ HTTP/1.1" 500 209788
ERROR 2025-06-13 09:34:50,325 log 1699457 126955793929920 Internal Server Error: /api/documents/documents/
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: documents_document

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/pagination.py", line 211, in paginate_queryset
    self.page = paginator.page(page_number)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 89, in page
    number = self.validate_number(number)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 70, in validate_number
    if number > self.num_pages:
                ^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 116, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
       ^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 110, in count
    return c()
           ^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/query.py", line 604, in count
    return self.query.get_count(using=self.db)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 644, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 626, in get_aggregation
    result = compiler.execute_sql(SINGLE)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: documents_document
ERROR 2025-06-13 09:34:50,326 basehttp 1699457 126955793929920 "GET /api/documents/documents/ HTTP/1.1" 500 209788
ERROR 2025-06-13 09:34:55,363 log 1699457 126955793929920 Internal Server Error: /api/documents/documents/
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: documents_document

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/pagination.py", line 211, in paginate_queryset
    self.page = paginator.page(page_number)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 89, in page
    number = self.validate_number(number)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 70, in validate_number
    if number > self.num_pages:
                ^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 116, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
       ^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 110, in count
    return c()
           ^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/query.py", line 604, in count
    return self.query.get_count(using=self.db)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 644, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 626, in get_aggregation
    result = compiler.execute_sql(SINGLE)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: documents_document
ERROR 2025-06-13 09:34:55,365 basehttp 1699457 126955793929920 "GET /api/documents/documents/ HTTP/1.1" 500 209788
ERROR 2025-06-13 09:34:56,403 log 1699457 126955793929920 Internal Server Error: /api/documents/documents/
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: documents_document

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/pagination.py", line 211, in paginate_queryset
    self.page = paginator.page(page_number)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 89, in page
    number = self.validate_number(number)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 70, in validate_number
    if number > self.num_pages:
                ^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 116, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
       ^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 110, in count
    return c()
           ^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/query.py", line 604, in count
    return self.query.get_count(using=self.db)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 644, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 626, in get_aggregation
    result = compiler.execute_sql(SINGLE)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: documents_document
ERROR 2025-06-13 09:34:56,405 basehttp 1699457 126955793929920 "GET /api/documents/documents/ HTTP/1.1" 500 209788
ERROR 2025-06-13 09:35:01,437 log 1699457 126955793929920 Internal Server Error: /api/documents/documents/
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: documents_document

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/pagination.py", line 211, in paginate_queryset
    self.page = paginator.page(page_number)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 89, in page
    number = self.validate_number(number)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 70, in validate_number
    if number > self.num_pages:
                ^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 116, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
       ^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 110, in count
    return c()
           ^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/query.py", line 604, in count
    return self.query.get_count(using=self.db)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 644, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 626, in get_aggregation
    result = compiler.execute_sql(SINGLE)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: documents_document
ERROR 2025-06-13 09:35:01,438 basehttp 1699457 126955793929920 "GET /api/documents/documents/ HTTP/1.1" 500 209788
ERROR 2025-06-13 09:35:02,473 log 1699457 126955793929920 Internal Server Error: /api/documents/documents/
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: documents_document

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/mixins.py", line 40, in list
    page = self.paginate_queryset(queryset)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/rest_framework/pagination.py", line 211, in paginate_queryset
    self.page = paginator.page(page_number)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 89, in page
    number = self.validate_number(number)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 70, in validate_number
    if number > self.num_pages:
                ^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 116, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
       ^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/core/paginator.py", line 110, in count
    return c()
           ^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/query.py", line 604, in count
    return self.query.get_count(using=self.db)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 644, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/query.py", line 626, in get_aggregation
    result = compiler.execute_sql(SINGLE)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/python311_notebook/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: documents_document
ERROR 2025-06-13 09:35:02,475 basehttp 1699457 126955793929920 "GET /api/documents/documents/ HTTP/1.1" 500 209788
WARNING 2025-06-13 09:35:06,657 log 1699457 126955793929920 Unauthorized: /api/documents/documents/
WARNING 2025-06-13 09:35:06,657 basehttp 1699457 126955793929920 "GET /api/documents/documents/ HTTP/1.1" 401 58
WARNING 2025-06-13 11:00:04,166 log 1699457 126955793929920 Unauthorized: /api/documents/documents/
WARNING 2025-06-13 11:00:04,166 basehttp 1699457 126955793929920 "GET /api/documents/documents/ HTTP/1.1" 401 58
INFO 2025-06-13 11:00:21,194 basehttp 1699457 126955793929920 "POST /api/users/login/ HTTP/1.1" 200 190
INFO 2025-06-13 11:00:31,567 basehttp 1699457 126955793929920 "GET /api/documents/documents/ HTTP/1.1" 200 52
WARNING 2025-06-13 11:02:11,581 log 1699457 126955802322624 Unauthorized: /api/documents/documents/
WARNING 2025-06-13 11:02:11,581 basehttp 1699457 126955802322624 "GET /api/documents/documents/ HTTP/1.1" 401 58
INFO 2025-06-13 11:02:15,533 basehttp 1699457 126955802322624 "POST /api/users/login/ HTTP/1.1" 200 190
INFO 2025-06-13 11:02:15,596 basehttp 1699457 126955802322624 "GET /api/documents/documents/ HTTP/1.1" 200 52
WARNING 2025-06-13 11:02:19,018 log 1699457 126955802322624 Unauthorized: /api/documents/documents/
WARNING 2025-06-13 11:02:19,018 basehttp 1699457 126955802322624 "GET /api/documents/documents/ HTTP/1.1" 401 58
INFO 2025-06-13 11:02:20,885 basehttp 1699457 126955802322624 "POST /api/users/login/ HTTP/1.1" 200 190
INFO 2025-06-13 11:02:20,943 basehttp 1699457 126955802322624 "GET /api/documents/documents/ HTTP/1.1" 200 52
WARNING 2025-06-13 11:07:30,519 log 1699457 126955802322624 Unauthorized: /api/documents/documents/
WARNING 2025-06-13 11:07:30,519 basehttp 1699457 126955802322624 "GET /api/documents/documents/ HTTP/1.1" 401 58
INFO 2025-06-13 11:07:32,503 basehttp 1699457 126955802322624 "POST /api/users/login/ HTTP/1.1" 200 190
INFO 2025-06-13 11:07:32,546 basehttp 1699457 126955802322624 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:31:00,357 autoreload 745787 135759012013888 Watching for file changes with StatReloader
INFO 2025-06-16 16:34:01,238 basehttp 745787 135758432171712 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-06-16 16:34:01,315 basehttp 745787 135758432171712 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4173
INFO 2025-06-16 16:34:01,381 basehttp 745787 135758345139904 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
INFO 2025-06-16 16:34:01,382 basehttp 745787 135758432171712 "GET /static/admin/css/base.css HTTP/1.1" 200 22120
INFO 2025-06-16 16:34:01,383 basehttp 745787 135758423779008 "GET /static/admin/css/responsive.css HTTP/1.1" 200 16565
INFO 2025-06-16 16:34:01,383 basehttp 745787 135758328354496 "GET /static/admin/css/login.css HTTP/1.1" 200 951
INFO 2025-06-16 16:34:01,383 basehttp 745787 135758336747200 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
INFO 2025-06-16 16:34:01,384 basehttp 745787 135758319961792 "GET /static/admin/js/theme.js HTTP/1.1" 200 1653
INFO 2025-06-16 16:34:01,392 basehttp 745787 135758319961792 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
WARNING 2025-06-16 16:34:01,468 log 745787 135758319961792 Not Found: /favicon.ico
WARNING 2025-06-16 16:34:01,468 basehttp 745787 135758319961792 "GET /favicon.ico HTTP/1.1" 404 3174
INFO 2025-06-16 16:34:05,662 basehttp 745787 135758319961792 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-06-16 16:34:05,678 basehttp 745787 135758319961792 "GET /admin/ HTTP/1.1" 200 10267
INFO 2025-06-16 16:34:05,732 basehttp 745787 135758319961792 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-06-16 16:34:05,779 basehttp 745787 135758319961792 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-06-16 16:34:05,780 basehttp 745787 135758432171712 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
INFO 2025-06-16 16:36:55,291 autoreload 751644 133532957636416 Watching for file changes with StatReloader
INFO 2025-06-16 16:37:09,780 basehttp 751644 133532375578304 "POST /api/users/login/ HTTP/1.1" 200 200
INFO 2025-06-16 16:37:09,882 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:38:36,700 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:38:41,730 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:38:46,741 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:38:51,750 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:38:56,760 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
WARNING 2025-06-16 16:38:58,607 log 751644 133532375578304 Unauthorized: /api/documents/documents/
WARNING 2025-06-16 16:38:58,607 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 401 74
INFO 2025-06-16 16:38:58,608 basehttp 751644 133532375578304 code 400, message Bad request syntax ('7252efd79203fe0a60baff20b0e56382f75394a8')
WARNING 2025-06-16 16:38:58,608 basehttp 751644 133532375578304 "7252efd79203fe0a60baff20b0e56382f75394a8" 400 -
INFO 2025-06-16 16:39:01,768 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:39:06,776 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:39:11,783 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:39:16,793 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:39:21,805 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:39:25,942 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:39:26,817 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:39:31,829 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:39:36,837 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:39:39,552 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:39:44,574 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:39:49,592 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
WARNING 2025-06-16 16:39:55,376 log 751644 133532375578304 Unauthorized: /api/documents/documents/
WARNING 2025-06-16 16:39:55,377 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 401 58
INFO 2025-06-16 16:40:06,012 basehttp 751644 133532375578304 "POST /api/users/login/ HTTP/1.1" 200 200
INFO 2025-06-16 16:40:06,052 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:40:11,060 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:40:16,072 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:40:21,082 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:40:26,094 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:40:31,106 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:40:36,117 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:40:41,126 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:40:46,137 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:40:51,151 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:40:56,167 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:41:01,189 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:41:06,205 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:41:11,218 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:41:16,234 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:41:21,249 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:41:26,262 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:41:31,274 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 52
INFO 2025-06-16 16:41:34,127 basehttp 751644 133532375578304 "POST /api/documents/documents/ HTTP/1.1" 201 1045
INFO 2025-06-16 16:41:34,140 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 1373
INFO 2025-06-16 16:41:39,159 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 1373
INFO 2025-06-16 16:41:44,175 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 1409
INFO 2025-06-16 16:41:49,185 basehttp 751644 133532375578304 "GET /api/documents/documents/ HTTP/1.1" 200 1409
INFO 2025-06-16 16:41:51,619 basehttp 751644 133532375578304 "GET /api/documents/documents/c13b991c-96c0-4fb6-bd57-90f531fba976/ HTTP/1.1" 200 142352
INFO 2025-06-16 16:41:51,669 basehttp 751644 133532375578304 "GET /api/documents/documents/c13b991c-96c0-4fb6-bd57-90f531fba976/sentences/ HTTP/1.1" 200 2
INFO 2025-06-16 16:41:53,086 basehttp 751644 133532359845568 "GET /api/documents/documents/c13b991c-96c0-4fb6-bd57-90f531fba976/sections/ HTTP/1.1" 200 140788
INFO 2025-06-16 16:41:53,205 basehttp 751644 133532375578304 "GET /media/pdfs/Build_a_Large_Language_Model_250210_003345.pdf HTTP/1.1" 200 17256903
INFO 2025-06-16 16:41:55,287 basehttp 751644 133532278060736 "GET /media/pdfs/Build_a_Large_Language_Model_250210_003345.pdf HTTP/1.1" 200 17256903
INFO 2025-06-16 16:43:08,509 basehttp 751644 133532269668032 "GET /api/documents/documents/c13b991c-96c0-4fb6-bd57-90f531fba976/sentences/ HTTP/1.1" 200 2
INFO 2025-06-16 16:43:12,096 basehttp 751644 133532359845568 "GET /api/documents/documents/c13b991c-96c0-4fb6-bd57-90f531fba976/ HTTP/1.1" 200 142352
INFO 2025-06-16 16:43:12,153 basehttp 751644 133532261275328 "GET /api/documents/documents/c13b991c-96c0-4fb6-bd57-90f531fba976/sections/ HTTP/1.1" 200 140788
INFO 2025-06-16 16:43:48,771 basehttp 751644 133532278060736 "GET /media/pdfs/Build_a_Large_Language_Model_250210_003345.pdf HTTP/1.1" 200 17256903
