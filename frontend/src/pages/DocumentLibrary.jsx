import React, { useState } from 'react'
import {
  Box,
  Container,
  <PERSON>po<PERSON>,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  LinearProgress,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
} from '@mui/material'
import {
  Upload as UploadIcon,
  PlayArrow as PlayIcon,
  MoreVert as MoreIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Book as BookIcon,
  Article as ArticleIcon,
  Description as DocumentIcon,
} from '@mui/icons-material'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useNavigate } from 'react-router-dom'
import { documentAPI } from '../services/api'
import DocumentUpload from '../components/documents/DocumentUpload'
import { formatFileSize, formatDate, getProcessingStatusColor } from '../utils/helpers'

const DocumentLibrary = () => {
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false)
  const [menuAnchor, setMenuAnchor] = useState(null)
  const [selectedDocument, setSelectedDocument] = useState(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)

  // Fetch documents
  const { data: documents = [], isLoading, error } = useQuery(
    'documents',
    documentAPI.getDocuments,
    {
      select: (response) => {
        // Handle paginated response - documents are in results array
        const data = response?.data?.results;
        return Array.isArray(data) ? data : [];
      },
      refetchInterval: 5000, // Refresh every 5 seconds to update processing status
    }
  )

  // Delete document mutation
  const deleteMutation = useMutation(
    (id) => documentAPI.deleteDocument(id),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('documents')
        setDeleteDialogOpen(false)
        setSelectedDocument(null)
      },
    }
  )

  // Reprocess document mutation
  const reprocessMutation = useMutation(
    (id) => documentAPI.reprocessDocument(id),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('documents')
      },
    }
  )

  const handleMenuOpen = (event, document) => {
    setMenuAnchor(event.currentTarget)
    setSelectedDocument(document)
  }

  const handleMenuClose = () => {
    setMenuAnchor(null)
    setSelectedDocument(null)
  }

  const handleReadDocument = (document) => {
    if (document.processing_status === 'completed') {
      navigate(`/documents/${document.id}/read`)
    }
  }

  const handleDownload = async (document) => {
    try {
      const response = await documentAPI.downloadDocument(document.id)
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `${document.title || 'document'}.pdf`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Download failed:', error)
    }
    handleMenuClose()
  }

  const handleReprocess = (document) => {
    reprocessMutation.mutate(document.id)
    handleMenuClose()
  }

  const handleDeleteClick = (document) => {
    setSelectedDocument(document)
    setDeleteDialogOpen(true)
    handleMenuClose()
  }

  const handleDeleteConfirm = () => {
    if (selectedDocument) {
      deleteMutation.mutate(selectedDocument.id)
    }
  }

  const getDocumentIcon = (documentType) => {
    switch (documentType) {
      case 'book':
        return <BookIcon />
      case 'academic':
      case 'article':
        return <ArticleIcon />
      default:
        return <DocumentIcon />
    }
  }

  const getStatusChip = (status) => {
    const color = getProcessingStatusColor(status)
    const labels = {
      pending: 'Pending',
      processing: 'Processing...',
      completed: 'Ready',
      failed: 'Failed',
    }

    return (
      <Chip
        label={labels[status] || status}
        color={color}
        size="small"
        variant={status === 'completed' ? 'filled' : 'outlined'}
      />
    )
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">
          Failed to load documents. Please try again.
        </Alert>
      </Container>
    )
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography variant="h4" component="h1">
          Document Library
        </Typography>
        <Button
          variant="contained"
          startIcon={<UploadIcon />}
          onClick={() => setUploadDialogOpen(true)}
          size="large"
        >
          Upload Document
        </Button>
      </Box>

      {/* Documents Grid */}
      {isLoading ? (
        <Box display="flex" justifyContent="center" py={4}>
          <LinearProgress sx={{ width: '50%' }} />
        </Box>
      ) : documents.length === 0 ? (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          py={8}
          textAlign="center"
        >
          <DocumentIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No documents yet
          </Typography>
          <Typography variant="body2" color="text.secondary" mb={3}>
            Upload your first PDF to start learning
          </Typography>
          <Button
            variant="contained"
            startIcon={<UploadIcon />}
            onClick={() => setUploadDialogOpen(true)}
          >
            Upload Document
          </Button>
        </Box>
      ) : (
        <Grid container spacing={3}>
          {documents.map((document) => (
            <Grid item xs={12} sm={6} md={4} key={document.id}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  cursor: document.processing_status === 'completed' ? 'pointer' : 'default',
                  '&:hover': {
                    boxShadow: document.processing_status === 'completed' ? 4 : 1,
                  },
                }}
                onClick={() => handleReadDocument(document)}
              >
                <CardContent sx={{ flex: 1 }}>
                  {/* Document Icon and Status */}
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                    <Box display="flex" alignItems="center" gap={1}>
                      {getDocumentIcon(document.document_type)}
                      {getStatusChip(document.processing_status)}
                    </Box>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleMenuOpen(e, document)
                      }}
                    >
                      <MoreIcon />
                    </IconButton>
                  </Box>

                  {/* Title and Author */}
                  <Typography variant="h6" component="h2" gutterBottom noWrap>
                    {document.title || 'Untitled Document'}
                  </Typography>
                  {document.author && (
                    <Typography variant="body2" color="text.secondary" gutterBottom noWrap>
                      by {document.author}
                    </Typography>
                  )}

                  {/* Description */}
                  {document.description && (
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        mb: 2,
                      }}
                    >
                      {document.description}
                    </Typography>
                  )}

                  {/* Processing Progress */}
                  {document.processing_status === 'processing' && (
                    <Box mb={2}>
                      <LinearProgress />
                      <Typography variant="caption" color="text.secondary" mt={1}>
                        Processing document...
                      </Typography>
                    </Box>
                  )}

                  {/* Document Stats */}
                  <Box display="flex" flexWrap="wrap" gap={1} mt={2}>
                    {document.page_count && (
                      <Chip label={`${document.page_count} pages`} size="small" variant="outlined" />
                    )}
                    {document.total_sentences > 0 && (
                      <Chip label={`${document.total_sentences} sentences`} size="small" variant="outlined" />
                    )}
                    {document.file_size_mb && (
                      <Chip label={formatFileSize(document.file_size_mb * 1024 * 1024)} size="small" variant="outlined" />
                    )}
                  </Box>

                  {/* Upload Date */}
                  <Typography variant="caption" color="text.secondary" display="block" mt={2}>
                    Uploaded {formatDate(document.created_at)}
                  </Typography>
                </CardContent>

                <CardActions>
                  {document.processing_status === 'completed' ? (
                    <Button
                      size="small"
                      startIcon={<PlayIcon />}
                      onClick={(e) => {
                        e.stopPropagation()
                        handleReadDocument(document)
                      }}
                    >
                      Start Reading
                    </Button>
                  ) : document.processing_status === 'failed' ? (
                    <Button
                      size="small"
                      startIcon={<RefreshIcon />}
                      onClick={(e) => {
                        e.stopPropagation()
                        handleReprocess(document)
                      }}
                      disabled={reprocessMutation.isLoading}
                    >
                      Retry
                    </Button>
                  ) : (
                    <Button size="small" disabled>
                      Processing...
                    </Button>
                  )}
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Document Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleDownload(selectedDocument)}>
          <DownloadIcon sx={{ mr: 1 }} />
          Download PDF
        </MenuItem>
        <MenuItem onClick={() => handleReprocess(selectedDocument)}>
          <RefreshIcon sx={{ mr: 1 }} />
          Reprocess
        </MenuItem>
        <MenuItem onClick={() => handleDeleteClick(selectedDocument)} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Upload Dialog */}
      <Dialog
        open={uploadDialogOpen}
        onClose={() => setUploadDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Upload Document</DialogTitle>
        <DialogContent>
          <DocumentUpload
            onUploadComplete={() => {
              setUploadDialogOpen(false)
              queryClient.invalidateQueries('documents')
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Document</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{selectedDocument?.title || 'this document'}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            disabled={deleteMutation.isLoading}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  )
}

export default DocumentLibrary
