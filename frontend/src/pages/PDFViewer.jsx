import React, { useState, useEffect } from 'react'
import {
  Box,
  AppBar,
  <PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  Divider,
  Chip,
  LinearProgress,
  Alert,
  Fab,
  Tooltip,
} from '@mui/material'
import {
  ArrowBack as BackIcon,
  MenuBook as MenuIcon,
  VolumeUp as AudioIcon,
  Psychology as ExplainIcon,
  Bookmark as BookmarkIcon,
  Settings as SettingsIcon,
  Close as CloseIcon,
} from '@mui/icons-material'
import { useParams, useNavigate } from 'react-router-dom'
import { useQuery } from 'react-query'
import { useLearningStore } from '../stores/learningStore'
import { documentAPI, learningAPI } from '../services/api'
import PDFRenderer from '../components/pdf/PDFRenderer'
import AudioPlayer from '../components/audio/AudioPlayer'
import ExplanationPanel from '../components/learning/ExplanationPanel'
import SentenceNavigation from '../components/learning/SentenceNavigation'

const PDFViewer = () => {
  const { documentId } = useParams()
  const navigate = useNavigate()
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [explanationPanelOpen, setExplanationPanelOpen] = useState(false)
  const [sessionId, setSessionId] = useState(null)

  const {
    currentDocument,
    setCurrentDocument,
    setSentences,
    setSections,
    currentSentenceIndex,
    setCurrentSentenceIndex,
    audioEnabled,
    setAudioEnabled,
    startSession,
    endSession,
    reset,
  } = useLearningStore()

  // Fetch document details
  const { data: document, isLoading: documentLoading, error: documentError } = useQuery(
    ['document', documentId],
    () => documentAPI.getDocument(documentId),
    {
      enabled: !!documentId,
      select: (response) => response.data,
      onSuccess: (data) => {
        setCurrentDocument(data)
      },
    }
  )

  // Fetch sentences
  const { data: sentences = [], isLoading: sentencesLoading } = useQuery(
    ['sentences', documentId],
    () => documentAPI.getSentences(documentId),
    {
      enabled: !!documentId && document?.processing_status === 'completed',
      select: (response) => response.data,
      onSuccess: (data) => {
        setSentences(data)
      },
    }
  )

  // Fetch sections
  const { data: sections = [] } = useQuery(
    ['sections', documentId],
    () => documentAPI.getSections(documentId),
    {
      enabled: !!documentId && document?.processing_status === 'completed',
      select: (response) => response.data,
      onSuccess: (data) => {
        setSections(data)
      },
    }
  )

  // Start learning session
  useEffect(() => {
    if (document && sentences.length > 0 && !sessionId) {
      const newSessionId = startSession(documentId)
      setSessionId(newSessionId)
    }

    return () => {
      if (sessionId) {
        endSession()
      }
    }
  }, [document, sentences, sessionId, documentId, startSession, endSession])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      reset()
    }
  }, [reset])

  const handleSentenceClick = (sentence) => {
    const index = sentences.findIndex(s => s.id === sentence.id)
    if (index !== -1) {
      setCurrentSentenceIndex(index)
    }
  }

  const handleSectionClick = (section) => {
    // Find first sentence in this section
    const firstSentence = sentences.find(s => s.section === section.id)
    if (firstSentence) {
      handleSentenceClick(firstSentence)
    }
  }

  const handleBack = () => {
    navigate('/documents')
  }

  const getDocumentUrl = () => {
    if (document?.file) {
      // Ensure the URL is properly constructed
      // If it's already a full URL, return as is
      if (document.file.startsWith('http')) {
        return document.file
      }
      // If it's a relative path, construct the full URL
      // Remove leading slash if present to avoid double slashes
      const filePath = document.file.startsWith('/') ? document.file.slice(1) : document.file
      return `${window.location.origin}/${filePath}`
    }
    return null
  }

  if (documentError) {
    return (
      <Box p={3}>
        <Alert severity="error">
          Failed to load document. Please try again.
        </Alert>
      </Box>
    )
  }

  if (documentLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="100vh">
        <LinearProgress sx={{ width: '50%' }} />
      </Box>
    )
  }

  if (!document) {
    return (
      <Box p={3}>
        <Alert severity="warning">
          Document not found.
        </Alert>
      </Box>
    )
  }

  if (document.processing_status !== 'completed') {
    return (
      <Box p={3}>
        <Alert severity="info">
          <Typography variant="h6" gutterBottom>
            Document is being processed
          </Typography>
          <Typography variant="body2" paragraph>
            Please wait while we extract and analyze the content. This usually takes a few minutes.
          </Typography>
          <LinearProgress sx={{ mt: 2 }} />
          <Typography variant="caption" color="text.secondary" mt={1}>
            Status: {document.processing_status}
          </Typography>
        </Alert>
      </Box>
    )
  }

  return (
    <Box sx={{ display: 'flex', height: '100vh', overflow: 'hidden' }}>
      {/* Top App Bar */}
      <AppBar position="fixed" sx={{ zIndex: 1201 }}>
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={handleBack}
            sx={{ mr: 2 }}
          >
            <BackIcon />
          </IconButton>
          
          <IconButton
            color="inherit"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>

          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" noWrap>
              {document.title || 'Untitled Document'}
            </Typography>
            {document.author && (
              <Typography variant="caption" color="inherit" opacity={0.7}>
                by {document.author}
              </Typography>
            )}
          </Box>

          <Box display="flex" alignItems="center" gap={1}>
            <Chip
              label={`${sentences.length} sentences`}
              size="small"
              variant="outlined"
              sx={{ color: 'white', borderColor: 'white' }}
            />
            
            <Tooltip title={audioEnabled ? 'Disable Audio' : 'Enable Audio'}>
              <IconButton
                color="inherit"
                onClick={() => setAudioEnabled(!audioEnabled)}
              >
                <AudioIcon color={audioEnabled ? 'inherit' : 'disabled'} />
              </IconButton>
            </Tooltip>

            <Tooltip title="AI Explanations">
              <IconButton
                color="inherit"
                onClick={() => setExplanationPanelOpen(!explanationPanelOpen)}
              >
                <ExplainIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      <Drawer
        variant="persistent"
        anchor="left"
        open={sidebarOpen}
        sx={{
          width: sidebarOpen ? 320 : 0,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: 320,
            boxSizing: 'border-box',
            mt: 8, // Account for AppBar height
          },
        }}
      >
        <Box sx={{ overflow: 'auto', height: '100%' }}>
          {/* Document Sections */}
          <Box p={2}>
            <Typography variant="h6" gutterBottom>
              Table of Contents
            </Typography>
            <List dense>
              {sections.map((section) => (
                <ListItemButton
                  key={section.id}
                  onClick={() => handleSectionClick(section)}
                  sx={{
                    pl: section.level * 2,
                    borderRadius: 1,
                    mb: 0.5,
                  }}
                >
                  <ListItemText
                    primary={section.title}
                    secondary={`Page ${section.page_start}`}
                    primaryTypographyProps={{
                      variant: section.level === 1 ? 'subtitle2' : 'body2',
                      fontWeight: section.level === 1 ? 'bold' : 'normal',
                    }}
                  />
                </ListItemButton>
              ))}
            </List>
          </Box>

          <Divider />

          {/* Sentence Navigation */}
          <SentenceNavigation
            sentences={sentences}
            currentIndex={currentSentenceIndex}
            onSentenceSelect={handleSentenceClick}
          />
        </Box>
      </Drawer>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          mt: 8, // Account for AppBar height
          ml: sidebarOpen ? 0 : 0,
          transition: 'margin 0.3s ease',
        }}
      >
        {/* PDF Viewer */}
        <Box sx={{ flex: 1, overflow: 'hidden' }}>
          {sentencesLoading ? (
            <Box display="flex" justifyContent="center" alignItems="center" height="100%">
              <LinearProgress sx={{ width: '50%' }} />
            </Box>
          ) : (
            <PDFRenderer
              documentUrl={getDocumentUrl()}
              onSentenceClick={handleSentenceClick}
              onPageChange={(page) => {
                // Update current page in store if needed
              }}
            />
          )}
        </Box>

        {/* Audio Player */}
        {audioEnabled && sentences.length > 0 && (
          <AudioPlayer
            onSentenceComplete={(sentence) => {
              // Handle sentence completion
              console.log('Sentence completed:', sentence)
            }}
            onAudioError={(error) => {
              console.error('Audio error:', error)
            }}
          />
        )}
      </Box>

      {/* Explanation Panel */}
      <ExplanationPanel
        open={explanationPanelOpen}
        onClose={() => setExplanationPanelOpen(false)}
      />

      {/* Floating Action Buttons */}
      <Box
        sx={{
          position: 'fixed',
          bottom: 16,
          right: explanationPanelOpen ? 432 : 16,
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          transition: 'right 0.3s ease',
        }}
      >
        <Fab
          color="primary"
          size="small"
          onClick={() => setExplanationPanelOpen(!explanationPanelOpen)}
        >
          {explanationPanelOpen ? <CloseIcon /> : <ExplainIcon />}
        </Fab>
        
        <Fab
          color="secondary"
          size="small"
          onClick={() => {
            // Add bookmark functionality
            console.log('Add bookmark')
          }}
        >
          <BookmarkIcon />
        </Fab>
      </Box>
    </Box>
  )
}

export default PDFViewer
