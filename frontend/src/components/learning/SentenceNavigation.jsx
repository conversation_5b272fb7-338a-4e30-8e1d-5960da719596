import React, { useState, useEffect, useRef } from 'react'
import {
  Box,
  Typo<PERSON>,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemIcon,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Tooltip,
  Collapse,
} from '@mui/material'
import {
  Search as SearchIcon,
  VolumeUp as AudioIcon,
  Psychology as ExplainIcon,
  Bookmark as BookmarkIcon,
  ExpandMore as ExpandIcon,
  ExpandLess as CollapseIcon,
  PlayArrow as PlayIcon,
} from '@mui/icons-material'
import { useLearningStore } from '../../stores/learningStore'

const SentenceNavigation = ({ sentences = [], currentIndex, onSentenceSelect }) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredSentences, setFilteredSentences] = useState([])
  const [expandedPages, setExpandedPages] = useState(new Set([1]))
  const [isInitialized, setIsInitialized] = useState(false)
  const currentItemRef = useRef(null)

  const { currentSentence } = useLearningStore()

  // Initialize filtered sentences when sentences prop changes
  useEffect(() => {
    setFilteredSentences(sentences || [])
    setIsInitialized(true)
  }, [sentences])

  // Filter sentences based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredSentences(sentences || [])
    } else {
      const filtered = (sentences || []).filter(sentence =>
        sentence.text.toLowerCase().includes(searchTerm.toLowerCase())
      )
      setFilteredSentences(filtered)
    }
  }, [searchTerm, sentences])

  // Don't render until initialized to prevent infinite loops
  if (!isInitialized) {
    return (
      <Box p={2} display="flex" justifyContent="center">
        <CircularProgress size={20} />
      </Box>
    )
  }

  // Group sentences by page
  const sentencesByPage = filteredSentences.reduce((acc, sentence) => {
    const page = sentence.page_number
    if (!acc[page]) {
      acc[page] = []
    }
    acc[page].push(sentence)
    return acc
  }, {})

  // Auto-scroll to current sentence
  useEffect(() => {
    if (currentItemRef.current) {
      currentItemRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      })
    }
  }, [currentIndex])

  // Auto-expand page containing current sentence
  useEffect(() => {
    if (currentSentence) {
      setExpandedPages(prev => new Set([...prev, currentSentence.page_number]))
    }
  }, [currentSentence])

  const handlePageToggle = (pageNumber) => {
    setExpandedPages(prev => {
      const newSet = new Set(prev)
      if (newSet.has(pageNumber)) {
        newSet.delete(pageNumber)
      } else {
        newSet.add(pageNumber)
      }
      return newSet
    })
  }

  const handleSentenceClick = (sentence) => {
    onSentenceSelect(sentence)
  }

  const getSentenceStatus = (sentence) => {
    const status = []
    
    if (sentence.has_audio) {
      status.push(
        <Tooltip key="audio" title="Has audio">
          <AudioIcon fontSize="small" color="primary" />
        </Tooltip>
      )
    }
    
    if (sentence.explanation) {
      status.push(
        <Tooltip key="explanation" title="Has explanation">
          <ExplainIcon fontSize="small" color="secondary" />
        </Tooltip>
      )
    }
    
    return status
  }

  const truncateText = (text, maxLength = 80) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
  }

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box p={2} pb={1}>
        <Typography variant="h6" gutterBottom>
          Sentences
        </Typography>
        
        {/* Search */}
        <TextField
          fullWidth
          size="small"
          placeholder="Search sentences..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon fontSize="small" />
              </InputAdornment>
            ),
          }}
        />
        
        {/* Stats */}
        <Box display="flex" gap={1} mt={1}>
          <Chip
            label={`${filteredSentences.length} sentences`}
            size="small"
            variant="outlined"
          />
          {searchTerm && (
            <Chip
              label="Filtered"
              size="small"
              color="primary"
              variant="outlined"
            />
          )}
        </Box>
      </Box>

      {/* Sentence List */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        <List dense>
          {Object.entries(sentencesByPage)
            .sort(([a], [b]) => parseInt(a) - parseInt(b))
            .map(([pageNumber, pageSentences]) => (
              <Box key={pageNumber}>
                {/* Page Header */}
                <ListItemButton
                  onClick={() => handlePageToggle(parseInt(pageNumber))}
                  sx={{
                    backgroundColor: 'action.hover',
                    borderBottom: 1,
                    borderColor: 'divider',
                  }}
                >
                  <ListItemText
                    primary={`Page ${pageNumber}`}
                    secondary={`${pageSentences.length} sentences`}
                    primaryTypographyProps={{
                      variant: 'subtitle2',
                      fontWeight: 'bold',
                    }}
                  />
                  {expandedPages.has(parseInt(pageNumber)) ? (
                    <CollapseIcon />
                  ) : (
                    <ExpandIcon />
                  )}
                </ListItemButton>

                {/* Page Sentences */}
                <Collapse in={expandedPages.has(parseInt(pageNumber))}>
                  {pageSentences.map((sentence) => {
                    const isCurrentSentence = sentence.sentence_index === currentIndex
                    
                    return (
                      <ListItem
                        key={sentence.id}
                        ref={isCurrentSentence ? currentItemRef : null}
                        disablePadding
                        sx={{
                          borderLeft: isCurrentSentence ? 4 : 0,
                          borderLeftColor: 'primary.main',
                          backgroundColor: isCurrentSentence ? 'action.selected' : 'transparent',
                        }}
                      >
                        <ListItemButton
                          onClick={() => handleSentenceClick(sentence)}
                          sx={{
                            pl: 3,
                            py: 1,
                            '&:hover': {
                              backgroundColor: 'action.hover',
                            },
                          }}
                        >
                          <Box sx={{ width: '100%' }}>
                            {/* Sentence Number and Status */}
                            <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
                              <Typography variant="caption" color="text.secondary">
                                #{sentence.sentence_index + 1}
                              </Typography>
                              <Box display="flex" gap={0.5}>
                                {getSentenceStatus(sentence)}
                              </Box>
                            </Box>
                            
                            {/* Sentence Text */}
                            <Typography
                              variant="body2"
                              sx={{
                                lineHeight: 1.4,
                                fontWeight: isCurrentSentence ? 'medium' : 'normal',
                              }}
                            >
                              {truncateText(sentence.text)}
                            </Typography>
                            
                            {/* Highlight search term */}
                            {searchTerm && sentence.text.toLowerCase().includes(searchTerm.toLowerCase()) && (
                              <Typography variant="caption" color="primary" display="block" mt={0.5}>
                                Match found
                              </Typography>
                            )}
                          </Box>
                        </ListItemButton>
                      </ListItem>
                    )
                  })}
                </Collapse>
              </Box>
            ))}
        </List>

        {/* No Results */}
        {filteredSentences.length === 0 && (
          <Box p={3} textAlign="center">
            <Typography variant="body2" color="text.secondary">
              {searchTerm ? 'No sentences match your search' : 'No sentences available'}
            </Typography>
          </Box>
        )}
      </Box>

      {/* Current Sentence Info */}
      {currentSentence && (
        <Box
          sx={{
            p: 2,
            borderTop: 1,
            borderColor: 'divider',
            backgroundColor: 'action.hover',
          }}
        >
          <Typography variant="caption" color="text.secondary" gutterBottom>
            Current Sentence
          </Typography>
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            #{currentSentence.sentence_index + 1} of {(sentences || []).length}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Page {currentSentence.page_number}
          </Typography>
        </Box>
      )}
    </Box>
  )
}

export default SentenceNavigation
