import React, { useEffect, useRef, useState, useCallback } from 'react'
import { Box, CircularProgress, Alert } from '@mui/material'
import * as pdfjsLib from 'pdfjs-dist'
import { fabric } from 'fabric'
import { useLearningStore } from '../../stores/learningStore'

// Set up PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.js',
  import.meta.url
).toString()

// Alternative worker setup for development
if (import.meta.env.DEV) {
  pdfjsLib.GlobalWorkerOptions.workerSrc = '/node_modules/pdfjs-dist/build/pdf.worker.min.js'
}

const PDFRenderer = ({ documentUrl, onSentenceClick, onPageChange }) => {
  const pdfCanvasRef = useRef(null)
  const fabricCanvasRef = useRef(null)
  const fabricCanvasElementRef = useRef(null)
  const [pdfDoc, setPdfDoc] = useState(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [scale, setScale] = useState(1.0)
  
  const {
    sentences,
    currentSentenceIndex,
    highlightEnabled,
    currentPage: storeCurrentPage,
  } = useLearningStore()

  // Initialize PDF document
  useEffect(() => {
    if (!documentUrl) return

    const loadPDF = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const pdf = await pdfjsLib.getDocument(documentUrl).promise
        setPdfDoc(pdf)
        setTotalPages(pdf.numPages)
        setCurrentPage(1)
        
      } catch (err) {
        console.error('Error loading PDF:', err)
        setError('Failed to load PDF document')
      } finally {
        setLoading(false)
      }
    }

    loadPDF()
  }, [documentUrl])

  // Initialize Fabric.js canvas
  useEffect(() => {
    if (!fabricCanvasElementRef.current) return

    const canvas = new fabric.Canvas(fabricCanvasElementRef.current, {
      selection: false,
      preserveObjectStacking: true,
      renderOnAddRemove: false,
    })

    fabricCanvasRef.current = canvas

    // Handle canvas clicks
    canvas.on('mouse:down', (e) => {
      if (e.target && e.target.sentenceData) {
        onSentenceClick?.(e.target.sentenceData)
      }
    })

    return () => {
      canvas.dispose()
    }
  }, [onSentenceClick])

  // Render PDF page
  const renderPage = useCallback(async (pageNum) => {
    if (!pdfDoc || !pdfCanvasRef.current) return

    try {
      const page = await pdfDoc.getPage(pageNum)
      const viewport = page.getViewport({ scale })

      // Set up PDF canvas
      const canvas = pdfCanvasRef.current
      const context = canvas.getContext('2d')
      canvas.height = viewport.height
      canvas.width = viewport.width
      
      // Update Fabric canvas size
      if (fabricCanvasRef.current) {
        fabricCanvasRef.current.setDimensions({
          width: viewport.width,
          height: viewport.height,
        })
      }

      // Render PDF page
      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      }
      
      await page.render(renderContext).promise
      
      // Add sentence highlights
      if (highlightEnabled) {
        addSentenceHighlights(pageNum, viewport)
      }
      
    } catch (err) {
      console.error('Error rendering page:', err)
      setError('Failed to render PDF page')
    }
  }, [pdfDoc, scale, highlightEnabled])

  // Add sentence highlights using Fabric.js
  const addSentenceHighlights = useCallback((pageNum, viewport) => {
    if (!fabricCanvasRef.current || !sentences.length) return

    const canvas = fabricCanvasRef.current
    
    // Clear existing highlights
    canvas.getObjects().forEach(obj => {
      if (obj.type === 'rect' && obj.isSentenceHighlight) {
        canvas.remove(obj)
      }
    })

    // Filter sentences for current page
    const pageSentences = sentences.filter(s => s.page_number === pageNum)

    pageSentences.forEach((sentence, index) => {
      const isActive = sentence.sentence_index === currentSentenceIndex
      
      // Create highlight rectangle
      const highlight = new fabric.Rect({
        left: sentence.bbox_x * viewport.scale,
        top: sentence.bbox_y * viewport.scale,
        width: sentence.bbox_width * viewport.scale,
        height: sentence.bbox_height * viewport.scale,
        fill: isActive ? 'rgba(76, 175, 80, 0.3)' : 'rgba(255, 255, 0, 0.2)',
        stroke: isActive ? '#4caf50' : '#ffeb3b',
        strokeWidth: isActive ? 3 : 1,
        selectable: false,
        evented: true,
        hoverCursor: 'pointer',
        isSentenceHighlight: true,
        sentenceData: sentence,
      })

      // Add hover effects
      highlight.on('mouseover', function() {
        if (!isActive) {
          this.set({
            fill: 'rgba(33, 150, 243, 0.3)',
            stroke: '#2196f3',
            strokeWidth: 2,
          })
          canvas.renderAll()
        }
      })

      highlight.on('mouseout', function() {
        if (!isActive) {
          this.set({
            fill: 'rgba(255, 255, 0, 0.2)',
            stroke: '#ffeb3b',
            strokeWidth: 1,
          })
          canvas.renderAll()
        }
      })

      canvas.add(highlight)
    })

    canvas.renderAll()
  }, [sentences, currentSentenceIndex])

  // Handle page changes
  useEffect(() => {
    if (storeCurrentPage !== currentPage) {
      setCurrentPage(storeCurrentPage)
    }
  }, [storeCurrentPage])

  // Render page when page number or scale changes
  useEffect(() => {
    if (pdfDoc && currentPage) {
      renderPage(currentPage)
      onPageChange?.(currentPage)
    }
  }, [pdfDoc, currentPage, scale, renderPage, onPageChange])

  // Update highlights when current sentence changes
  useEffect(() => {
    if (highlightEnabled && sentences.length > 0) {
      const currentSentence = sentences[currentSentenceIndex]
      if (currentSentence && currentSentence.page_number !== currentPage) {
        setCurrentPage(currentSentence.page_number)
      } else {
        // Re-render highlights for current page
        renderPage(currentPage)
      }
    }
  }, [currentSentenceIndex, highlightEnabled, sentences, currentPage, renderPage])

  // Navigation functions
  const goToPage = (pageNum) => {
    if (pageNum >= 1 && pageNum <= totalPages) {
      setCurrentPage(pageNum)
    }
  }

  const nextPage = () => goToPage(currentPage + 1)
  const prevPage = () => goToPage(currentPage - 1)

  // Zoom functions
  const zoomIn = () => setScale(prev => Math.min(prev + 0.25, 3.0))
  const zoomOut = () => setScale(prev => Math.max(prev - 0.25, 0.5))
  const resetZoom = () => setScale(1.0)

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="100%">
        <CircularProgress />
      </Box>
    )
  }

  if (error) {
    return (
      <Box p={2}>
        <Alert severity="error">{error}</Alert>
      </Box>
    )
  }

  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        height: '100%',
        overflow: 'auto',
        backgroundColor: '#f5f5f5',
      }}
    >
      <Box
        sx={{
          position: 'relative',
          display: 'inline-block',
          margin: '20px auto',
          boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
        }}
      >
        {/* PDF Canvas */}
        <canvas
          ref={pdfCanvasRef}
          style={{
            display: 'block',
            backgroundColor: 'white',
          }}
        />

        {/* Fabric.js Overlay Canvas */}
        <canvas
          ref={fabricCanvasElementRef}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            pointerEvents: highlightEnabled ? 'auto' : 'none',
          }}
        />
      </Box>
    </Box>
  )
}

export default PDFRenderer
