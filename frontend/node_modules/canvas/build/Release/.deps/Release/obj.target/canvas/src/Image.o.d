cmd_Release/obj.target/canvas/src/Image.o := g++ -o Release/obj.target/canvas/src/Image.o ../src/Image.cc '-DNODE_GYP_MODULE_NAME=canvas' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-DV8_DEPRECATION_WARNINGS' '-DV8_IMMINENT_DEPRECATION_WARNINGS' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-D__STDC_FORMAT_MACROS' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DHAVE_GIF' '-DHAVE_JPEG' '-DHAVE_RSVG' '-DBUILDING_NODE_EXTENSION' -I/github/home/<USER>/node-gyp/20.0.0/include/node -I/github/home/<USER>/node-gyp/20.0.0/src -I/github/home/<USER>/node-gyp/20.0.0/deps/openssl/config -I/github/home/<USER>/node-gyp/20.0.0/deps/openssl/openssl/include -I/github/home/<USER>/node-gyp/20.0.0/deps/uv/include -I/github/home/<USER>/node-gyp/20.0.0/deps/zlib -I/github/home/<USER>/node-gyp/20.0.0/deps/v8/include -I../node_modules/nan -I/usr/local/include/cairo -I/usr/local/include -I/usr/local/include/glib-2.0 -I/usr/local/lib/glib-2.0/include -I/usr/local/include/pixman-1 -I/usr/local/include/freetype2 -I/usr/local/include/libpng16 -I/usr/local/include/pango-1.0 -I/usr/local/include/fribidi -I/usr/local/include/harfbuzz -I/usr/local/include/librsvg-2.0 -I/usr/local/include/gdk-pixbuf-2.0  -fPIC -pthread -Wall -Wextra -Wno-unused-parameter -m64 -O3 -fno-omit-frame-pointer -fno-rtti -std=gnu++17 -MMD -MF ./Release/.deps/Release/obj.target/canvas/src/Image.o.d.raw   -c
Release/obj.target/canvas/src/Image.o: ../src/Image.cc ../src/Image.h \
 /usr/local/include/cairo/cairo.h \
 /usr/local/include/cairo/cairo-version.h \
 /usr/local/include/cairo/cairo-features.h \
 /usr/local/include/cairo/cairo-deprecated.h ../src/CanvasError.h \
 ../node_modules/nan/nan.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/node_version.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/uv.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/uv/errno.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/uv/version.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/uv/unix.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/uv/threadpool.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/uv/linux.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/node.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/cppgc/common.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8config.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-array-buffer.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-local-handle.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-internal.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-version.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8config.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-object.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-maybe.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-persistent-handle.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-weak-callback-info.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-primitive.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-data.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-value.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-traced-handle.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-container.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-context.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-snapshot.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-date.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-debug.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-script.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-callbacks.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-promise.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-message.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-exception.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-extension.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-external.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-function.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-function-callback.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-template.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-memory-span.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-initialization.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-isolate.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-embedder-heap.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-microtask.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-statistics.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-unwinder.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-embedder-state-scope.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-platform.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-json.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-locker.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-microtask-queue.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-primitive-object.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-proxy.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-regexp.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-typed-array.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-value-serializer.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8-wasm.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/node_version.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/node_api.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/js_native_api.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/js_native_api_types.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/node_api_types.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/node_buffer.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/node.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/node_object_wrap.h \
 ../node_modules/nan/nan_callbacks.h \
 ../node_modules/nan/nan_callbacks_12_inl.h \
 ../node_modules/nan/nan_maybe_43_inl.h \
 ../node_modules/nan/nan_converters.h \
 ../node_modules/nan/nan_converters_43_inl.h \
 ../node_modules/nan/nan_new.h \
 ../node_modules/nan/nan_implementation_12_inl.h \
 ../node_modules/nan/nan_persistent_12_inl.h \
 ../node_modules/nan/nan_weak.h ../node_modules/nan/nan_object_wrap.h \
 ../node_modules/nan/nan_private.h \
 ../node_modules/nan/nan_typedarray_contents.h \
 ../node_modules/nan/nan_json.h ../node_modules/nan/nan_scriptorigin.h \
 /github/home/<USER>/node-gyp/20.0.0/include/node/v8.h \
 /usr/local/include/librsvg-2.0/librsvg/rsvg.h \
 /usr/local/include/glib-2.0/glib-object.h \
 /usr/local/include/glib-2.0/gobject/gbinding.h \
 /usr/local/include/glib-2.0/glib.h \
 /usr/local/include/glib-2.0/glib/galloca.h \
 /usr/local/include/glib-2.0/glib/gtypes.h \
 /usr/local/lib/glib-2.0/include/glibconfig.h \
 /usr/local/include/glib-2.0/glib/gmacros.h \
 /usr/local/include/glib-2.0/glib/gversionmacros.h \
 /usr/local/include/glib-2.0/glib/garray.h \
 /usr/local/include/glib-2.0/glib/gasyncqueue.h \
 /usr/local/include/glib-2.0/glib/gthread.h \
 /usr/local/include/glib-2.0/glib/gatomic.h \
 /usr/local/include/glib-2.0/glib/gerror.h \
 /usr/local/include/glib-2.0/glib/gquark.h \
 /usr/local/include/glib-2.0/glib/gutils.h \
 /usr/local/include/glib-2.0/glib/gbacktrace.h \
 /usr/local/include/glib-2.0/glib/gbase64.h \
 /usr/local/include/glib-2.0/glib/gbitlock.h \
 /usr/local/include/glib-2.0/glib/gbookmarkfile.h \
 /usr/local/include/glib-2.0/glib/gdatetime.h \
 /usr/local/include/glib-2.0/glib/gtimezone.h \
 /usr/local/include/glib-2.0/glib/gbytes.h \
 /usr/local/include/glib-2.0/glib/gcharset.h \
 /usr/local/include/glib-2.0/glib/gchecksum.h \
 /usr/local/include/glib-2.0/glib/gconvert.h \
 /usr/local/include/glib-2.0/glib/gdataset.h \
 /usr/local/include/glib-2.0/glib/gdate.h \
 /usr/local/include/glib-2.0/glib/gdir.h \
 /usr/local/include/glib-2.0/glib/genviron.h \
 /usr/local/include/glib-2.0/glib/gfileutils.h \
 /usr/local/include/glib-2.0/glib/ggettext.h \
 /usr/local/include/glib-2.0/glib/ghash.h \
 /usr/local/include/glib-2.0/glib/glist.h \
 /usr/local/include/glib-2.0/glib/gmem.h \
 /usr/local/include/glib-2.0/glib/gnode.h \
 /usr/local/include/glib-2.0/glib/ghmac.h \
 /usr/local/include/glib-2.0/glib/gchecksum.h \
 /usr/local/include/glib-2.0/glib/ghook.h \
 /usr/local/include/glib-2.0/glib/ghostutils.h \
 /usr/local/include/glib-2.0/glib/giochannel.h \
 /usr/local/include/glib-2.0/glib/gmain.h \
 /usr/local/include/glib-2.0/glib/gpoll.h \
 /usr/local/include/glib-2.0/glib/gslist.h \
 /usr/local/include/glib-2.0/glib/gstring.h \
 /usr/local/include/glib-2.0/glib/gunicode.h \
 /usr/local/include/glib-2.0/glib/gkeyfile.h \
 /usr/local/include/glib-2.0/glib/gmappedfile.h \
 /usr/local/include/glib-2.0/glib/gmarkup.h \
 /usr/local/include/glib-2.0/glib/gmessages.h \
 /usr/local/include/glib-2.0/glib/gvariant.h \
 /usr/local/include/glib-2.0/glib/gvarianttype.h \
 /usr/local/include/glib-2.0/glib/goption.h \
 /usr/local/include/glib-2.0/glib/gpattern.h \
 /usr/local/include/glib-2.0/glib/gprimes.h \
 /usr/local/include/glib-2.0/glib/gqsort.h \
 /usr/local/include/glib-2.0/glib/gqueue.h \
 /usr/local/include/glib-2.0/glib/grand.h \
 /usr/local/include/glib-2.0/glib/grcbox.h \
 /usr/local/include/glib-2.0/glib/grefcount.h \
 /usr/local/include/glib-2.0/glib/grefstring.h \
 /usr/local/include/glib-2.0/glib/gmem.h \
 /usr/local/include/glib-2.0/glib/gmacros.h \
 /usr/local/include/glib-2.0/glib/gregex.h \
 /usr/local/include/glib-2.0/glib/gscanner.h \
 /usr/local/include/glib-2.0/glib/gsequence.h \
 /usr/local/include/glib-2.0/glib/gshell.h \
 /usr/local/include/glib-2.0/glib/gslice.h \
 /usr/local/include/glib-2.0/glib/gspawn.h \
 /usr/local/include/glib-2.0/glib/gstrfuncs.h \
 /usr/local/include/glib-2.0/glib/gstringchunk.h \
 /usr/local/include/glib-2.0/glib/gstrvbuilder.h \
 /usr/local/include/glib-2.0/glib/gtestutils.h \
 /usr/local/include/glib-2.0/glib/gthreadpool.h \
 /usr/local/include/glib-2.0/glib/gtimer.h \
 /usr/local/include/glib-2.0/glib/gtrashstack.h \
 /usr/local/include/glib-2.0/glib/gtree.h \
 /usr/local/include/glib-2.0/glib/guri.h \
 /usr/local/include/glib-2.0/glib/guuid.h \
 /usr/local/include/glib-2.0/glib/gversion.h \
 /usr/local/include/glib-2.0/glib/deprecated/gallocator.h \
 /usr/local/include/glib-2.0/glib/deprecated/gcache.h \
 /usr/local/include/glib-2.0/glib/deprecated/gcompletion.h \
 /usr/local/include/glib-2.0/glib/deprecated/gmain.h \
 /usr/local/include/glib-2.0/glib/deprecated/grel.h \
 /usr/local/include/glib-2.0/glib/deprecated/gthread.h \
 /usr/local/include/glib-2.0/glib/glib-autocleanups.h \
 /usr/local/include/glib-2.0/gobject/gobject.h \
 /usr/local/include/glib-2.0/gobject/gtype.h \
 /usr/local/include/glib-2.0/gobject/gvalue.h \
 /usr/local/include/glib-2.0/gobject/gparam.h \
 /usr/local/include/glib-2.0/gobject/gclosure.h \
 /usr/local/include/glib-2.0/gobject/gsignal.h \
 /usr/local/include/glib-2.0/gobject/gmarshal.h \
 /usr/local/include/glib-2.0/gobject/gboxed.h \
 /usr/local/include/glib-2.0/gobject/glib-types.h \
 /usr/local/include/glib-2.0/gobject/genums.h \
 /usr/local/include/glib-2.0/gobject/glib-enumtypes.h \
 /usr/local/include/glib-2.0/gobject/gparamspecs.h \
 /usr/local/include/glib-2.0/gobject/gsourceclosure.h \
 /usr/local/include/glib-2.0/gobject/gtypemodule.h \
 /usr/local/include/glib-2.0/gobject/gtypeplugin.h \
 /usr/local/include/glib-2.0/gobject/gvaluearray.h \
 /usr/local/include/glib-2.0/gobject/gvaluetypes.h \
 /usr/local/include/glib-2.0/gobject/gobject-autocleanups.h \
 /usr/local/include/glib-2.0/gio/gio.h \
 /usr/local/include/glib-2.0/gio/giotypes.h \
 /usr/local/include/glib-2.0/gio/gioenums.h \
 /usr/local/include/glib-2.0/gio/gaction.h \
 /usr/local/include/glib-2.0/gio/gactiongroup.h \
 /usr/local/include/glib-2.0/gio/gactiongroupexporter.h \
 /usr/local/include/glib-2.0/gio/gactionmap.h \
 /usr/local/include/glib-2.0/gio/gappinfo.h \
 /usr/local/include/glib-2.0/gio/gapplication.h \
 /usr/local/include/glib-2.0/gio/gapplicationcommandline.h \
 /usr/local/include/glib-2.0/gio/gasyncinitable.h \
 /usr/local/include/glib-2.0/gio/ginitable.h \
 /usr/local/include/glib-2.0/gio/gasyncresult.h \
 /usr/local/include/glib-2.0/gio/gbufferedinputstream.h \
 /usr/local/include/glib-2.0/gio/gfilterinputstream.h \
 /usr/local/include/glib-2.0/gio/ginputstream.h \
 /usr/local/include/glib-2.0/gio/gbufferedoutputstream.h \
 /usr/local/include/glib-2.0/gio/gfilteroutputstream.h \
 /usr/local/include/glib-2.0/gio/goutputstream.h \
 /usr/local/include/glib-2.0/gio/gbytesicon.h \
 /usr/local/include/glib-2.0/gio/gcancellable.h \
 /usr/local/include/glib-2.0/gio/gcharsetconverter.h \
 /usr/local/include/glib-2.0/gio/gconverter.h \
 /usr/local/include/glib-2.0/gio/gcontenttype.h \
 /usr/local/include/glib-2.0/gio/gconverterinputstream.h \
 /usr/local/include/glib-2.0/gio/gconverteroutputstream.h \
 /usr/local/include/glib-2.0/gio/gcredentials.h \
 /usr/local/include/glib-2.0/gio/gdatagrambased.h \
 /usr/local/include/glib-2.0/gio/gdatainputstream.h \
 /usr/local/include/glib-2.0/gio/gdataoutputstream.h \
 /usr/local/include/glib-2.0/gio/gdbusactiongroup.h \
 /usr/local/include/glib-2.0/gio/giotypes.h \
 /usr/local/include/glib-2.0/gio/gdbusaddress.h \
 /usr/local/include/glib-2.0/gio/gdbusauthobserver.h \
 /usr/local/include/glib-2.0/gio/gdbusconnection.h \
 /usr/local/include/glib-2.0/gio/gdbuserror.h \
 /usr/local/include/glib-2.0/gio/gdbusinterface.h \
 /usr/local/include/glib-2.0/gio/gdbusinterfaceskeleton.h \
 /usr/local/include/glib-2.0/gio/gdbusintrospection.h \
 /usr/local/include/glib-2.0/gio/gdbusmenumodel.h \
 /usr/local/include/glib-2.0/gio/gdbusmessage.h \
 /usr/local/include/glib-2.0/gio/gdbusmethodinvocation.h \
 /usr/local/include/glib-2.0/gio/gdbusnameowning.h \
 /usr/local/include/glib-2.0/gio/gdbusnamewatching.h \
 /usr/local/include/glib-2.0/gio/gdbusobject.h \
 /usr/local/include/glib-2.0/gio/gdbusobjectmanager.h \
 /usr/local/include/glib-2.0/gio/gdbusobjectmanagerclient.h \
 /usr/local/include/glib-2.0/gio/gdbusobjectmanagerserver.h \
 /usr/local/include/glib-2.0/gio/gdbusobjectproxy.h \
 /usr/local/include/glib-2.0/gio/gdbusobjectskeleton.h \
 /usr/local/include/glib-2.0/gio/gdbusproxy.h \
 /usr/local/include/glib-2.0/gio/gdbusserver.h \
 /usr/local/include/glib-2.0/gio/gdbusutils.h \
 /usr/local/include/glib-2.0/gio/gdrive.h \
 /usr/local/include/glib-2.0/gio/gdtlsclientconnection.h \
 /usr/local/include/glib-2.0/gio/gdtlsconnection.h \
 /usr/local/include/glib-2.0/gio/gdtlsserverconnection.h \
 /usr/local/include/glib-2.0/gio/gemblemedicon.h \
 /usr/local/include/glib-2.0/gio/gicon.h \
 /usr/local/include/glib-2.0/gio/gemblem.h \
 /usr/local/include/glib-2.0/gio/gfile.h \
 /usr/local/include/glib-2.0/gio/gfileattribute.h \
 /usr/local/include/glib-2.0/gio/gfileenumerator.h \
 /usr/local/include/glib-2.0/gio/gfileicon.h \
 /usr/local/include/glib-2.0/gio/gfileinfo.h \
 /usr/local/include/glib-2.0/gio/gfileinputstream.h \
 /usr/local/include/glib-2.0/gio/gfileiostream.h \
 /usr/local/include/glib-2.0/gio/giostream.h \
 /usr/local/include/glib-2.0/gio/gioerror.h \
 /usr/local/include/glib-2.0/gio/gfilemonitor.h \
 /usr/local/include/glib-2.0/gio/gfilenamecompleter.h \
 /usr/local/include/glib-2.0/gio/gfileoutputstream.h \
 /usr/local/include/glib-2.0/gio/ginetaddress.h \
 /usr/local/include/glib-2.0/gio/ginetaddressmask.h \
 /usr/local/include/glib-2.0/gio/ginetsocketaddress.h \
 /usr/local/include/glib-2.0/gio/gsocketaddress.h \
 /usr/local/include/glib-2.0/gio/gioenumtypes.h \
 /usr/local/include/glib-2.0/gio/giomodule.h \
 /usr/local/include/glib-2.0/gmodule.h \
 /usr/local/include/glib-2.0/gio/gioscheduler.h \
 /usr/local/include/glib-2.0/gio/glistmodel.h \
 /usr/local/include/glib-2.0/gio/gliststore.h \
 /usr/local/include/glib-2.0/gio/gloadableicon.h \
 /usr/local/include/glib-2.0/gio/gmemoryinputstream.h \
 /usr/local/include/glib-2.0/gio/gmemorymonitor.h \
 /usr/local/include/glib-2.0/gio/gmemoryoutputstream.h \
 /usr/local/include/glib-2.0/gio/gmenu.h \
 /usr/local/include/glib-2.0/gio/gmenumodel.h \
 /usr/local/include/glib-2.0/gio/gmenuexporter.h \
 /usr/local/include/glib-2.0/gio/gmount.h \
 /usr/local/include/glib-2.0/gio/gmountoperation.h \
 /usr/local/include/glib-2.0/gio/gnativesocketaddress.h \
 /usr/local/include/glib-2.0/gio/gnativevolumemonitor.h \
 /usr/local/include/glib-2.0/gio/gvolumemonitor.h \
 /usr/local/include/glib-2.0/gio/gnetworkaddress.h \
 /usr/local/include/glib-2.0/gio/gnetworkmonitor.h \
 /usr/local/include/glib-2.0/gio/gnetworkservice.h \
 /usr/local/include/glib-2.0/gio/gnotification.h \
 /usr/local/include/glib-2.0/gio/gpermission.h \
 /usr/local/include/glib-2.0/gio/gpollableinputstream.h \
 /usr/local/include/glib-2.0/gio/gpollableoutputstream.h \
 /usr/local/include/glib-2.0/gio/gpollableutils.h \
 /usr/local/include/glib-2.0/gio/gpropertyaction.h \
 /usr/local/include/glib-2.0/gio/gproxy.h \
 /usr/local/include/glib-2.0/gio/gproxyaddress.h \
 /usr/local/include/glib-2.0/gio/gproxyaddressenumerator.h \
 /usr/local/include/glib-2.0/gio/gsocketaddressenumerator.h \
 /usr/local/include/glib-2.0/gio/gproxyresolver.h \
 /usr/local/include/glib-2.0/gio/gremoteactiongroup.h \
 /usr/local/include/glib-2.0/gio/gresolver.h \
 /usr/local/include/glib-2.0/gio/gresource.h \
 /usr/local/include/glib-2.0/gio/gseekable.h \
 /usr/local/include/glib-2.0/gio/gsettings.h \
 /usr/local/include/glib-2.0/gio/gsettingsschema.h \
 /usr/local/include/glib-2.0/gio/gsimpleaction.h \
 /usr/local/include/glib-2.0/gio/gsimpleactiongroup.h \
 /usr/local/include/glib-2.0/gio/gactiongroup.h \
 /usr/local/include/glib-2.0/gio/gactionmap.h \
 /usr/local/include/glib-2.0/gio/gsimpleasyncresult.h \
 /usr/local/include/glib-2.0/gio/gsimpleiostream.h \
 /usr/local/include/glib-2.0/gio/gsimplepermission.h \
 /usr/local/include/glib-2.0/gio/gsimpleproxyresolver.h \
 /usr/local/include/glib-2.0/gio/gsocket.h \
 /usr/local/include/glib-2.0/gio/gsocketclient.h \
 /usr/local/include/glib-2.0/gio/gsocketconnectable.h \
 /usr/local/include/glib-2.0/gio/gsocketconnection.h \
 /usr/local/include/glib-2.0/gio/gsocketcontrolmessage.h \
 /usr/local/include/glib-2.0/gio/gsocketlistener.h \
 /usr/local/include/glib-2.0/gio/gsocketservice.h \
 /usr/local/include/glib-2.0/gio/gsrvtarget.h \
 /usr/local/include/glib-2.0/gio/gsubprocess.h \
 /usr/local/include/glib-2.0/gio/gsubprocesslauncher.h \
 /usr/local/include/glib-2.0/gio/gtask.h \
 /usr/local/include/glib-2.0/gio/gtcpconnection.h \
 /usr/local/include/glib-2.0/gio/gtcpwrapperconnection.h \
 /usr/local/include/glib-2.0/gio/gtestdbus.h \
 /usr/local/include/glib-2.0/gio/gthemedicon.h \
 /usr/local/include/glib-2.0/gio/gthreadedsocketservice.h \
 /usr/local/include/glib-2.0/gio/gtlsbackend.h \
 /usr/local/include/glib-2.0/gio/gtlscertificate.h \
 /usr/local/include/glib-2.0/gio/gtlsclientconnection.h \
 /usr/local/include/glib-2.0/gio/gtlsconnection.h \
 /usr/local/include/glib-2.0/gio/gtlsdatabase.h \
 /usr/local/include/glib-2.0/gio/gtlsfiledatabase.h \
 /usr/local/include/glib-2.0/gio/gtlsinteraction.h \
 /usr/local/include/glib-2.0/gio/gtlspassword.h \
 /usr/local/include/glib-2.0/gio/gtlsserverconnection.h \
 /usr/local/include/glib-2.0/gio/gvfs.h \
 /usr/local/include/glib-2.0/gio/gvolume.h \
 /usr/local/include/glib-2.0/gio/gzlibcompressor.h \
 /usr/local/include/glib-2.0/gio/gzlibdecompressor.h \
 /usr/local/include/glib-2.0/gio/gio-autocleanups.h \
 /usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf.h \
 /usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-macros.h \
 /usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-features.h \
 /usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-core.h \
 /usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-transform.h \
 /usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-animation.h \
 /usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-simple-anim.h \
 /usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-io.h \
 /usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-loader.h \
 /usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-enum-types.h \
 /usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-autocleanups.h \
 /usr/local/include/librsvg-2.0/librsvg/librsvg-features.h \
 /usr/local/include/librsvg-2.0/librsvg/rsvg-cairo.h \
 ../src/bmp/BMPParser.h ../src/Canvas.h ../src/backend/Backend.h \
 ../src/backend/../dll_visibility.h ../src/dll_visibility.h \
 /usr/local/include/pango-1.0/pango/pangocairo.h \
 /usr/local/include/pango-1.0/pango/pango.h \
 /usr/local/include/pango-1.0/pango/pango-attributes.h \
 /usr/local/include/pango-1.0/pango/pango-font.h \
 /usr/local/include/pango-1.0/pango/pango-coverage.h \
 /usr/local/include/pango-1.0/pango/pango-version-macros.h \
 /usr/local/include/pango-1.0/pango/pango-features.h \
 /usr/local/include/harfbuzz/hb.h /usr/local/include/harfbuzz/hb-blob.h \
 /usr/local/include/harfbuzz/hb-common.h \
 /usr/local/include/harfbuzz/hb-buffer.h \
 /usr/local/include/harfbuzz/hb-unicode.h \
 /usr/local/include/harfbuzz/hb-font.h \
 /usr/local/include/harfbuzz/hb-face.h \
 /usr/local/include/harfbuzz/hb-set.h \
 /usr/local/include/harfbuzz/hb-draw.h /usr/local/include/harfbuzz/hb.h \
 /usr/local/include/harfbuzz/hb-deprecated.h \
 /usr/local/include/harfbuzz/hb-map.h \
 /usr/local/include/harfbuzz/hb-shape.h \
 /usr/local/include/harfbuzz/hb-shape-plan.h \
 /usr/local/include/harfbuzz/hb-style.h \
 /usr/local/include/harfbuzz/hb-version.h \
 /usr/local/include/pango-1.0/pango/pango-types.h \
 /usr/local/include/pango-1.0/pango/pango-gravity.h \
 /usr/local/include/pango-1.0/pango/pango-matrix.h \
 /usr/local/include/pango-1.0/pango/pango-script.h \
 /usr/local/include/pango-1.0/pango/pango-language.h \
 /usr/local/include/pango-1.0/pango/pango-bidi-type.h \
 /usr/local/include/pango-1.0/pango/pango-direction.h \
 /usr/local/include/pango-1.0/pango/pango-break.h \
 /usr/local/include/pango-1.0/pango/pango-item.h \
 /usr/local/include/pango-1.0/pango/pango-context.h \
 /usr/local/include/pango-1.0/pango/pango-fontmap.h \
 /usr/local/include/pango-1.0/pango/pango-fontset.h \
 /usr/local/include/pango-1.0/pango/pango-engine.h \
 /usr/local/include/pango-1.0/pango/pango-glyph.h \
 /usr/local/include/pango-1.0/pango/pango-enum-types.h \
 /usr/local/include/pango-1.0/pango/pango-glyph-item.h \
 /usr/local/include/pango-1.0/pango/pango-layout.h \
 /usr/local/include/pango-1.0/pango/pango-tabs.h \
 /usr/local/include/pango-1.0/pango/pango-renderer.h \
 /usr/local/include/pango-1.0/pango/pango-utils.h
../src/Image.cc:
../src/Image.h:
/usr/local/include/cairo/cairo.h:
/usr/local/include/cairo/cairo-version.h:
/usr/local/include/cairo/cairo-features.h:
/usr/local/include/cairo/cairo-deprecated.h:
../src/CanvasError.h:
../node_modules/nan/nan.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/node_version.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/uv.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/uv/errno.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/uv/version.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/uv/unix.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/uv/threadpool.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/uv/linux.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/node.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/cppgc/common.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8config.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-array-buffer.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-local-handle.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-internal.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-version.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8config.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-object.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-maybe.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-persistent-handle.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-weak-callback-info.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-primitive.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-data.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-value.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-traced-handle.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-container.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-context.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-snapshot.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-date.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-debug.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-script.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-callbacks.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-promise.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-message.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-exception.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-extension.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-external.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-function.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-function-callback.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-template.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-memory-span.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-initialization.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-isolate.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-embedder-heap.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-microtask.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-statistics.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-unwinder.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-embedder-state-scope.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-platform.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-json.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-locker.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-microtask-queue.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-primitive-object.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-proxy.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-regexp.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-typed-array.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-value-serializer.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8-wasm.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/node_version.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/node_api.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/js_native_api.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/js_native_api_types.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/node_api_types.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/node_buffer.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/node.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/node_object_wrap.h:
../node_modules/nan/nan_callbacks.h:
../node_modules/nan/nan_callbacks_12_inl.h:
../node_modules/nan/nan_maybe_43_inl.h:
../node_modules/nan/nan_converters.h:
../node_modules/nan/nan_converters_43_inl.h:
../node_modules/nan/nan_new.h:
../node_modules/nan/nan_implementation_12_inl.h:
../node_modules/nan/nan_persistent_12_inl.h:
../node_modules/nan/nan_weak.h:
../node_modules/nan/nan_object_wrap.h:
../node_modules/nan/nan_private.h:
../node_modules/nan/nan_typedarray_contents.h:
../node_modules/nan/nan_json.h:
../node_modules/nan/nan_scriptorigin.h:
/github/home/<USER>/node-gyp/20.0.0/include/node/v8.h:
/usr/local/include/librsvg-2.0/librsvg/rsvg.h:
/usr/local/include/glib-2.0/glib-object.h:
/usr/local/include/glib-2.0/gobject/gbinding.h:
/usr/local/include/glib-2.0/glib.h:
/usr/local/include/glib-2.0/glib/galloca.h:
/usr/local/include/glib-2.0/glib/gtypes.h:
/usr/local/lib/glib-2.0/include/glibconfig.h:
/usr/local/include/glib-2.0/glib/gmacros.h:
/usr/local/include/glib-2.0/glib/gversionmacros.h:
/usr/local/include/glib-2.0/glib/garray.h:
/usr/local/include/glib-2.0/glib/gasyncqueue.h:
/usr/local/include/glib-2.0/glib/gthread.h:
/usr/local/include/glib-2.0/glib/gatomic.h:
/usr/local/include/glib-2.0/glib/gerror.h:
/usr/local/include/glib-2.0/glib/gquark.h:
/usr/local/include/glib-2.0/glib/gutils.h:
/usr/local/include/glib-2.0/glib/gbacktrace.h:
/usr/local/include/glib-2.0/glib/gbase64.h:
/usr/local/include/glib-2.0/glib/gbitlock.h:
/usr/local/include/glib-2.0/glib/gbookmarkfile.h:
/usr/local/include/glib-2.0/glib/gdatetime.h:
/usr/local/include/glib-2.0/glib/gtimezone.h:
/usr/local/include/glib-2.0/glib/gbytes.h:
/usr/local/include/glib-2.0/glib/gcharset.h:
/usr/local/include/glib-2.0/glib/gchecksum.h:
/usr/local/include/glib-2.0/glib/gconvert.h:
/usr/local/include/glib-2.0/glib/gdataset.h:
/usr/local/include/glib-2.0/glib/gdate.h:
/usr/local/include/glib-2.0/glib/gdir.h:
/usr/local/include/glib-2.0/glib/genviron.h:
/usr/local/include/glib-2.0/glib/gfileutils.h:
/usr/local/include/glib-2.0/glib/ggettext.h:
/usr/local/include/glib-2.0/glib/ghash.h:
/usr/local/include/glib-2.0/glib/glist.h:
/usr/local/include/glib-2.0/glib/gmem.h:
/usr/local/include/glib-2.0/glib/gnode.h:
/usr/local/include/glib-2.0/glib/ghmac.h:
/usr/local/include/glib-2.0/glib/gchecksum.h:
/usr/local/include/glib-2.0/glib/ghook.h:
/usr/local/include/glib-2.0/glib/ghostutils.h:
/usr/local/include/glib-2.0/glib/giochannel.h:
/usr/local/include/glib-2.0/glib/gmain.h:
/usr/local/include/glib-2.0/glib/gpoll.h:
/usr/local/include/glib-2.0/glib/gslist.h:
/usr/local/include/glib-2.0/glib/gstring.h:
/usr/local/include/glib-2.0/glib/gunicode.h:
/usr/local/include/glib-2.0/glib/gkeyfile.h:
/usr/local/include/glib-2.0/glib/gmappedfile.h:
/usr/local/include/glib-2.0/glib/gmarkup.h:
/usr/local/include/glib-2.0/glib/gmessages.h:
/usr/local/include/glib-2.0/glib/gvariant.h:
/usr/local/include/glib-2.0/glib/gvarianttype.h:
/usr/local/include/glib-2.0/glib/goption.h:
/usr/local/include/glib-2.0/glib/gpattern.h:
/usr/local/include/glib-2.0/glib/gprimes.h:
/usr/local/include/glib-2.0/glib/gqsort.h:
/usr/local/include/glib-2.0/glib/gqueue.h:
/usr/local/include/glib-2.0/glib/grand.h:
/usr/local/include/glib-2.0/glib/grcbox.h:
/usr/local/include/glib-2.0/glib/grefcount.h:
/usr/local/include/glib-2.0/glib/grefstring.h:
/usr/local/include/glib-2.0/glib/gmem.h:
/usr/local/include/glib-2.0/glib/gmacros.h:
/usr/local/include/glib-2.0/glib/gregex.h:
/usr/local/include/glib-2.0/glib/gscanner.h:
/usr/local/include/glib-2.0/glib/gsequence.h:
/usr/local/include/glib-2.0/glib/gshell.h:
/usr/local/include/glib-2.0/glib/gslice.h:
/usr/local/include/glib-2.0/glib/gspawn.h:
/usr/local/include/glib-2.0/glib/gstrfuncs.h:
/usr/local/include/glib-2.0/glib/gstringchunk.h:
/usr/local/include/glib-2.0/glib/gstrvbuilder.h:
/usr/local/include/glib-2.0/glib/gtestutils.h:
/usr/local/include/glib-2.0/glib/gthreadpool.h:
/usr/local/include/glib-2.0/glib/gtimer.h:
/usr/local/include/glib-2.0/glib/gtrashstack.h:
/usr/local/include/glib-2.0/glib/gtree.h:
/usr/local/include/glib-2.0/glib/guri.h:
/usr/local/include/glib-2.0/glib/guuid.h:
/usr/local/include/glib-2.0/glib/gversion.h:
/usr/local/include/glib-2.0/glib/deprecated/gallocator.h:
/usr/local/include/glib-2.0/glib/deprecated/gcache.h:
/usr/local/include/glib-2.0/glib/deprecated/gcompletion.h:
/usr/local/include/glib-2.0/glib/deprecated/gmain.h:
/usr/local/include/glib-2.0/glib/deprecated/grel.h:
/usr/local/include/glib-2.0/glib/deprecated/gthread.h:
/usr/local/include/glib-2.0/glib/glib-autocleanups.h:
/usr/local/include/glib-2.0/gobject/gobject.h:
/usr/local/include/glib-2.0/gobject/gtype.h:
/usr/local/include/glib-2.0/gobject/gvalue.h:
/usr/local/include/glib-2.0/gobject/gparam.h:
/usr/local/include/glib-2.0/gobject/gclosure.h:
/usr/local/include/glib-2.0/gobject/gsignal.h:
/usr/local/include/glib-2.0/gobject/gmarshal.h:
/usr/local/include/glib-2.0/gobject/gboxed.h:
/usr/local/include/glib-2.0/gobject/glib-types.h:
/usr/local/include/glib-2.0/gobject/genums.h:
/usr/local/include/glib-2.0/gobject/glib-enumtypes.h:
/usr/local/include/glib-2.0/gobject/gparamspecs.h:
/usr/local/include/glib-2.0/gobject/gsourceclosure.h:
/usr/local/include/glib-2.0/gobject/gtypemodule.h:
/usr/local/include/glib-2.0/gobject/gtypeplugin.h:
/usr/local/include/glib-2.0/gobject/gvaluearray.h:
/usr/local/include/glib-2.0/gobject/gvaluetypes.h:
/usr/local/include/glib-2.0/gobject/gobject-autocleanups.h:
/usr/local/include/glib-2.0/gio/gio.h:
/usr/local/include/glib-2.0/gio/giotypes.h:
/usr/local/include/glib-2.0/gio/gioenums.h:
/usr/local/include/glib-2.0/gio/gaction.h:
/usr/local/include/glib-2.0/gio/gactiongroup.h:
/usr/local/include/glib-2.0/gio/gactiongroupexporter.h:
/usr/local/include/glib-2.0/gio/gactionmap.h:
/usr/local/include/glib-2.0/gio/gappinfo.h:
/usr/local/include/glib-2.0/gio/gapplication.h:
/usr/local/include/glib-2.0/gio/gapplicationcommandline.h:
/usr/local/include/glib-2.0/gio/gasyncinitable.h:
/usr/local/include/glib-2.0/gio/ginitable.h:
/usr/local/include/glib-2.0/gio/gasyncresult.h:
/usr/local/include/glib-2.0/gio/gbufferedinputstream.h:
/usr/local/include/glib-2.0/gio/gfilterinputstream.h:
/usr/local/include/glib-2.0/gio/ginputstream.h:
/usr/local/include/glib-2.0/gio/gbufferedoutputstream.h:
/usr/local/include/glib-2.0/gio/gfilteroutputstream.h:
/usr/local/include/glib-2.0/gio/goutputstream.h:
/usr/local/include/glib-2.0/gio/gbytesicon.h:
/usr/local/include/glib-2.0/gio/gcancellable.h:
/usr/local/include/glib-2.0/gio/gcharsetconverter.h:
/usr/local/include/glib-2.0/gio/gconverter.h:
/usr/local/include/glib-2.0/gio/gcontenttype.h:
/usr/local/include/glib-2.0/gio/gconverterinputstream.h:
/usr/local/include/glib-2.0/gio/gconverteroutputstream.h:
/usr/local/include/glib-2.0/gio/gcredentials.h:
/usr/local/include/glib-2.0/gio/gdatagrambased.h:
/usr/local/include/glib-2.0/gio/gdatainputstream.h:
/usr/local/include/glib-2.0/gio/gdataoutputstream.h:
/usr/local/include/glib-2.0/gio/gdbusactiongroup.h:
/usr/local/include/glib-2.0/gio/giotypes.h:
/usr/local/include/glib-2.0/gio/gdbusaddress.h:
/usr/local/include/glib-2.0/gio/gdbusauthobserver.h:
/usr/local/include/glib-2.0/gio/gdbusconnection.h:
/usr/local/include/glib-2.0/gio/gdbuserror.h:
/usr/local/include/glib-2.0/gio/gdbusinterface.h:
/usr/local/include/glib-2.0/gio/gdbusinterfaceskeleton.h:
/usr/local/include/glib-2.0/gio/gdbusintrospection.h:
/usr/local/include/glib-2.0/gio/gdbusmenumodel.h:
/usr/local/include/glib-2.0/gio/gdbusmessage.h:
/usr/local/include/glib-2.0/gio/gdbusmethodinvocation.h:
/usr/local/include/glib-2.0/gio/gdbusnameowning.h:
/usr/local/include/glib-2.0/gio/gdbusnamewatching.h:
/usr/local/include/glib-2.0/gio/gdbusobject.h:
/usr/local/include/glib-2.0/gio/gdbusobjectmanager.h:
/usr/local/include/glib-2.0/gio/gdbusobjectmanagerclient.h:
/usr/local/include/glib-2.0/gio/gdbusobjectmanagerserver.h:
/usr/local/include/glib-2.0/gio/gdbusobjectproxy.h:
/usr/local/include/glib-2.0/gio/gdbusobjectskeleton.h:
/usr/local/include/glib-2.0/gio/gdbusproxy.h:
/usr/local/include/glib-2.0/gio/gdbusserver.h:
/usr/local/include/glib-2.0/gio/gdbusutils.h:
/usr/local/include/glib-2.0/gio/gdrive.h:
/usr/local/include/glib-2.0/gio/gdtlsclientconnection.h:
/usr/local/include/glib-2.0/gio/gdtlsconnection.h:
/usr/local/include/glib-2.0/gio/gdtlsserverconnection.h:
/usr/local/include/glib-2.0/gio/gemblemedicon.h:
/usr/local/include/glib-2.0/gio/gicon.h:
/usr/local/include/glib-2.0/gio/gemblem.h:
/usr/local/include/glib-2.0/gio/gfile.h:
/usr/local/include/glib-2.0/gio/gfileattribute.h:
/usr/local/include/glib-2.0/gio/gfileenumerator.h:
/usr/local/include/glib-2.0/gio/gfileicon.h:
/usr/local/include/glib-2.0/gio/gfileinfo.h:
/usr/local/include/glib-2.0/gio/gfileinputstream.h:
/usr/local/include/glib-2.0/gio/gfileiostream.h:
/usr/local/include/glib-2.0/gio/giostream.h:
/usr/local/include/glib-2.0/gio/gioerror.h:
/usr/local/include/glib-2.0/gio/gfilemonitor.h:
/usr/local/include/glib-2.0/gio/gfilenamecompleter.h:
/usr/local/include/glib-2.0/gio/gfileoutputstream.h:
/usr/local/include/glib-2.0/gio/ginetaddress.h:
/usr/local/include/glib-2.0/gio/ginetaddressmask.h:
/usr/local/include/glib-2.0/gio/ginetsocketaddress.h:
/usr/local/include/glib-2.0/gio/gsocketaddress.h:
/usr/local/include/glib-2.0/gio/gioenumtypes.h:
/usr/local/include/glib-2.0/gio/giomodule.h:
/usr/local/include/glib-2.0/gmodule.h:
/usr/local/include/glib-2.0/gio/gioscheduler.h:
/usr/local/include/glib-2.0/gio/glistmodel.h:
/usr/local/include/glib-2.0/gio/gliststore.h:
/usr/local/include/glib-2.0/gio/gloadableicon.h:
/usr/local/include/glib-2.0/gio/gmemoryinputstream.h:
/usr/local/include/glib-2.0/gio/gmemorymonitor.h:
/usr/local/include/glib-2.0/gio/gmemoryoutputstream.h:
/usr/local/include/glib-2.0/gio/gmenu.h:
/usr/local/include/glib-2.0/gio/gmenumodel.h:
/usr/local/include/glib-2.0/gio/gmenuexporter.h:
/usr/local/include/glib-2.0/gio/gmount.h:
/usr/local/include/glib-2.0/gio/gmountoperation.h:
/usr/local/include/glib-2.0/gio/gnativesocketaddress.h:
/usr/local/include/glib-2.0/gio/gnativevolumemonitor.h:
/usr/local/include/glib-2.0/gio/gvolumemonitor.h:
/usr/local/include/glib-2.0/gio/gnetworkaddress.h:
/usr/local/include/glib-2.0/gio/gnetworkmonitor.h:
/usr/local/include/glib-2.0/gio/gnetworkservice.h:
/usr/local/include/glib-2.0/gio/gnotification.h:
/usr/local/include/glib-2.0/gio/gpermission.h:
/usr/local/include/glib-2.0/gio/gpollableinputstream.h:
/usr/local/include/glib-2.0/gio/gpollableoutputstream.h:
/usr/local/include/glib-2.0/gio/gpollableutils.h:
/usr/local/include/glib-2.0/gio/gpropertyaction.h:
/usr/local/include/glib-2.0/gio/gproxy.h:
/usr/local/include/glib-2.0/gio/gproxyaddress.h:
/usr/local/include/glib-2.0/gio/gproxyaddressenumerator.h:
/usr/local/include/glib-2.0/gio/gsocketaddressenumerator.h:
/usr/local/include/glib-2.0/gio/gproxyresolver.h:
/usr/local/include/glib-2.0/gio/gremoteactiongroup.h:
/usr/local/include/glib-2.0/gio/gresolver.h:
/usr/local/include/glib-2.0/gio/gresource.h:
/usr/local/include/glib-2.0/gio/gseekable.h:
/usr/local/include/glib-2.0/gio/gsettings.h:
/usr/local/include/glib-2.0/gio/gsettingsschema.h:
/usr/local/include/glib-2.0/gio/gsimpleaction.h:
/usr/local/include/glib-2.0/gio/gsimpleactiongroup.h:
/usr/local/include/glib-2.0/gio/gactiongroup.h:
/usr/local/include/glib-2.0/gio/gactionmap.h:
/usr/local/include/glib-2.0/gio/gsimpleasyncresult.h:
/usr/local/include/glib-2.0/gio/gsimpleiostream.h:
/usr/local/include/glib-2.0/gio/gsimplepermission.h:
/usr/local/include/glib-2.0/gio/gsimpleproxyresolver.h:
/usr/local/include/glib-2.0/gio/gsocket.h:
/usr/local/include/glib-2.0/gio/gsocketclient.h:
/usr/local/include/glib-2.0/gio/gsocketconnectable.h:
/usr/local/include/glib-2.0/gio/gsocketconnection.h:
/usr/local/include/glib-2.0/gio/gsocketcontrolmessage.h:
/usr/local/include/glib-2.0/gio/gsocketlistener.h:
/usr/local/include/glib-2.0/gio/gsocketservice.h:
/usr/local/include/glib-2.0/gio/gsrvtarget.h:
/usr/local/include/glib-2.0/gio/gsubprocess.h:
/usr/local/include/glib-2.0/gio/gsubprocesslauncher.h:
/usr/local/include/glib-2.0/gio/gtask.h:
/usr/local/include/glib-2.0/gio/gtcpconnection.h:
/usr/local/include/glib-2.0/gio/gtcpwrapperconnection.h:
/usr/local/include/glib-2.0/gio/gtestdbus.h:
/usr/local/include/glib-2.0/gio/gthemedicon.h:
/usr/local/include/glib-2.0/gio/gthreadedsocketservice.h:
/usr/local/include/glib-2.0/gio/gtlsbackend.h:
/usr/local/include/glib-2.0/gio/gtlscertificate.h:
/usr/local/include/glib-2.0/gio/gtlsclientconnection.h:
/usr/local/include/glib-2.0/gio/gtlsconnection.h:
/usr/local/include/glib-2.0/gio/gtlsdatabase.h:
/usr/local/include/glib-2.0/gio/gtlsfiledatabase.h:
/usr/local/include/glib-2.0/gio/gtlsinteraction.h:
/usr/local/include/glib-2.0/gio/gtlspassword.h:
/usr/local/include/glib-2.0/gio/gtlsserverconnection.h:
/usr/local/include/glib-2.0/gio/gvfs.h:
/usr/local/include/glib-2.0/gio/gvolume.h:
/usr/local/include/glib-2.0/gio/gzlibcompressor.h:
/usr/local/include/glib-2.0/gio/gzlibdecompressor.h:
/usr/local/include/glib-2.0/gio/gio-autocleanups.h:
/usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf.h:
/usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-macros.h:
/usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-features.h:
/usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-core.h:
/usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-transform.h:
/usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-animation.h:
/usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-simple-anim.h:
/usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-io.h:
/usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-loader.h:
/usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-enum-types.h:
/usr/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-autocleanups.h:
/usr/local/include/librsvg-2.0/librsvg/librsvg-features.h:
/usr/local/include/librsvg-2.0/librsvg/rsvg-cairo.h:
../src/bmp/BMPParser.h:
../src/Canvas.h:
../src/backend/Backend.h:
../src/backend/../dll_visibility.h:
../src/dll_visibility.h:
/usr/local/include/pango-1.0/pango/pangocairo.h:
/usr/local/include/pango-1.0/pango/pango.h:
/usr/local/include/pango-1.0/pango/pango-attributes.h:
/usr/local/include/pango-1.0/pango/pango-font.h:
/usr/local/include/pango-1.0/pango/pango-coverage.h:
/usr/local/include/pango-1.0/pango/pango-version-macros.h:
/usr/local/include/pango-1.0/pango/pango-features.h:
/usr/local/include/harfbuzz/hb.h:
/usr/local/include/harfbuzz/hb-blob.h:
/usr/local/include/harfbuzz/hb-common.h:
/usr/local/include/harfbuzz/hb-buffer.h:
/usr/local/include/harfbuzz/hb-unicode.h:
/usr/local/include/harfbuzz/hb-font.h:
/usr/local/include/harfbuzz/hb-face.h:
/usr/local/include/harfbuzz/hb-set.h:
/usr/local/include/harfbuzz/hb-draw.h:
/usr/local/include/harfbuzz/hb.h:
/usr/local/include/harfbuzz/hb-deprecated.h:
/usr/local/include/harfbuzz/hb-map.h:
/usr/local/include/harfbuzz/hb-shape.h:
/usr/local/include/harfbuzz/hb-shape-plan.h:
/usr/local/include/harfbuzz/hb-style.h:
/usr/local/include/harfbuzz/hb-version.h:
/usr/local/include/pango-1.0/pango/pango-types.h:
/usr/local/include/pango-1.0/pango/pango-gravity.h:
/usr/local/include/pango-1.0/pango/pango-matrix.h:
/usr/local/include/pango-1.0/pango/pango-script.h:
/usr/local/include/pango-1.0/pango/pango-language.h:
/usr/local/include/pango-1.0/pango/pango-bidi-type.h:
/usr/local/include/pango-1.0/pango/pango-direction.h:
/usr/local/include/pango-1.0/pango/pango-break.h:
/usr/local/include/pango-1.0/pango/pango-item.h:
/usr/local/include/pango-1.0/pango/pango-context.h:
/usr/local/include/pango-1.0/pango/pango-fontmap.h:
/usr/local/include/pango-1.0/pango/pango-fontset.h:
/usr/local/include/pango-1.0/pango/pango-engine.h:
/usr/local/include/pango-1.0/pango/pango-glyph.h:
/usr/local/include/pango-1.0/pango/pango-enum-types.h:
/usr/local/include/pango-1.0/pango/pango-glyph-item.h:
/usr/local/include/pango-1.0/pango/pango-layout.h:
/usr/local/include/pango-1.0/pango/pango-tabs.h:
/usr/local/include/pango-1.0/pango/pango-renderer.h:
/usr/local/include/pango-1.0/pango/pango-utils.h:
