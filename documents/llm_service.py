"""
LLM Service for enhanced document processing using Amazon Bedrock.
"""
import json
import logging
import boto3
from typing import List, Dict, Any, Optional
from django.conf import settings

logger = logging.getLogger(__name__)


class DocumentLLMService:
    """Service for LLM-powered document analysis and processing."""
    
    def __init__(self):
        """Initialize the Bedrock client."""
        self.bedrock_client = None
        self.model_id = settings.BEDROCK_MODEL_ID

        # Only initialize if AWS credentials are available
        if settings.AWS_ACCESS_KEY_ID and settings.AWS_SECRET_ACCESS_KEY:
            try:
                self.bedrock_client = boto3.client(
                    'bedrock-runtime',
                    region_name=settings.BEDROCK_REGION,
                    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
                )
                logger.info("Bedrock client initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize Bedrock client: {e}")
        else:
            logger.info("AWS credentials not available, LLM features will use fallback methods")
    
    def _call_bedrock(self, prompt: str, max_tokens: int = 4000) -> str:
        """Make a call to Bedrock with the given prompt."""
        if not self.bedrock_client:
            raise Exception("Bedrock client not available")

        try:
            # Prepare the request body for Claude
            body = {
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": max_tokens,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.1,  # Low temperature for consistent results
                "top_p": 0.9
            }
            
            response = self.bedrock_client.invoke_model(
                modelId=self.model_id,
                body=json.dumps(body)
            )
            
            response_body = json.loads(response['body'].read())
            return response_body['content'][0]['text']
            
        except Exception as e:
            logger.error(f"Error calling Bedrock: {e}")
            raise
    
    def analyze_document_structure(self, raw_text_blocks: List[Dict]) -> Dict[str, Any]:
        """
        Use LLM to analyze document structure and identify sections, headings, etc.
        
        Args:
            raw_text_blocks: List of text blocks with metadata (font, size, position, etc.)
            
        Returns:
            Structured analysis of the document
        """
        # Prepare text blocks for analysis
        text_summary = []
        for i, block in enumerate(raw_text_blocks[:50]):  # Analyze first 50 blocks
            text_summary.append({
                'index': i,
                'text': block.get('text', '')[:200],  # First 200 chars
                'font_size': block.get('font_size', 0),
                'font_name': block.get('font_name', ''),
                'page': block.get('page', 1),
                'is_bold': block.get('is_bold', False),
                'position_y': block.get('bbox', {}).get('y', 0)
            })
        
        prompt = f"""
Analyze this document structure and identify the hierarchy of sections and headings.

Document text blocks (first 50):
{json.dumps(text_summary, indent=2)}

Please analyze and return a JSON response with:
1. Document type (book, academic_paper, article, manual, report, etc.)
2. Main sections with their hierarchy levels (1=main chapter, 2=subsection, etc.)
3. Heading patterns (font sizes, styles that indicate headings)
4. Table of contents structure if identifiable

Return ONLY valid JSON in this format:
{{
    "document_type": "book|academic_paper|article|manual|report|other",
    "heading_patterns": [
        {{"font_size": 16, "font_name": "Arial-Bold", "level": 1, "description": "Main chapters"}},
        {{"font_size": 14, "font_name": "Arial-Bold", "level": 2, "description": "Subsections"}}
    ],
    "sections": [
        {{
            "title": "Section Title",
            "level": 1,
            "start_block_index": 5,
            "estimated_page": 1,
            "confidence": 0.9
        }}
    ],
    "body_text_pattern": {{"font_size": 12, "font_name": "Arial"}}
}}
"""
        
        try:
            response = self._call_bedrock(prompt, max_tokens=3000)
            # Extract JSON from response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                logger.warning("Could not extract JSON from LLM response")
                return self._fallback_structure_analysis(raw_text_blocks)
        except Exception as e:
            logger.error(f"Error in LLM structure analysis: {e}")
            return self._fallback_structure_analysis(raw_text_blocks)
    
    def extract_and_clean_paragraphs(self, raw_content: List[Dict]) -> List[Dict]:
        """
        Use LLM to identify, extract, and clean paragraphs from raw content.
        
        Args:
            raw_content: Raw content blocks from PDF extraction
            
        Returns:
            List of cleaned paragraph objects
        """
        # Group content by page for processing
        pages_content = {}
        for item in raw_content:
            page = item.get('page', 1)
            if page not in pages_content:
                pages_content[page] = []
            pages_content[page].append(item)
        
        all_paragraphs = []
        
        # Process each page
        for page_num, page_content in list(pages_content.items())[:10]:  # Process first 10 pages
            paragraphs = self._extract_page_paragraphs(page_num, page_content)
            all_paragraphs.extend(paragraphs)
        
        return all_paragraphs
    
    def _extract_page_paragraphs(self, page_num: int, page_content: List[Dict]) -> List[Dict]:
        """Extract paragraphs from a single page using LLM."""
        # Prepare content for LLM analysis
        content_blocks = []
        for i, block in enumerate(page_content):
            if block.get('type') in ['text', 'paragraph']:
                content_blocks.append({
                    'index': i,
                    'text': block.get('text', ''),
                    'font_size': block.get('font_size', 12),
                    'position': block.get('bbox', {}),
                    'type': block.get('type', 'text')
                })
        
        if not content_blocks:
            return []
        
        prompt = f"""
Analyze this page content and identify coherent paragraphs. Clean up any OCR errors, merge fragmented text, and ensure proper paragraph boundaries.

Page {page_num} content blocks:
{json.dumps(content_blocks[:20], indent=2)}

Please:
1. Identify complete paragraphs by merging related text blocks
2. Clean up OCR errors and formatting issues
3. Remove headers, footers, and page numbers
4. Ensure proper sentence boundaries
5. Classify content type (paragraph, heading, caption, etc.)

Return ONLY valid JSON in this format:
{{
    "paragraphs": [
        {{
            "text": "Complete cleaned paragraph text...",
            "type": "paragraph|heading|caption|list_item|other",
            "confidence": 0.9,
            "page": {page_num},
            "source_blocks": [0, 1, 2]
        }}
    ]
}}
"""
        
        try:
            response = self._call_bedrock(prompt, max_tokens=3000)
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                result = json.loads(json_str)
                return result.get('paragraphs', [])
        except Exception as e:
            logger.error(f"Error in LLM paragraph extraction for page {page_num}: {e}")
        
        return []
    
    def segment_sentences(self, text: str) -> List[str]:
        """
        Use LLM for accurate sentence segmentation.
        
        Args:
            text: Input text to segment
            
        Returns:
            List of sentences
        """
        if not text or len(text.strip()) < 10:
            return []
        
        prompt = f"""
Segment this text into individual sentences. Handle abbreviations, numbers, and complex punctuation correctly.

Text to segment:
{text}

Return ONLY valid JSON in this format:
{{
    "sentences": [
        "First complete sentence.",
        "Second complete sentence.",
        "Third sentence with proper handling of Dr. Smith's work."
    ]
}}
"""
        
        try:
            response = self._call_bedrock(prompt, max_tokens=2000)
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                result = json.loads(json_str)
                return result.get('sentences', [])
        except Exception as e:
            logger.error(f"Error in LLM sentence segmentation: {e}")
            # Fallback to simple splitting
            return self._simple_sentence_split(text)
    
    def _simple_sentence_split(self, text: str) -> List[str]:
        """Fallback simple sentence splitting."""
        import re
        sentences = re.split(r'[.!?]+\s+', text)
        return [s.strip() for s in sentences if len(s.strip()) > 10]
    
    def _fallback_structure_analysis(self, raw_text_blocks: List[Dict]) -> Dict[str, Any]:
        """Fallback structure analysis when LLM fails."""
        return {
            "document_type": "other",
            "heading_patterns": [],
            "sections": [],
            "body_text_pattern": {"font_size": 12, "font_name": "Arial"}
        }


# Global instance
llm_service = DocumentLLMService()
