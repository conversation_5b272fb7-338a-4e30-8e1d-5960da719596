"""
Celery tasks for document processing.
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path
from celery import shared_task
from django.conf import settings
from django.utils import timezone

from .models import Document, DocumentSection, Sentence, DocumentImage

# Import the existing buddy PDF processor
from src.buddy.pipeline.processor import PDFProcessor
from src.buddy.llm.bedrock_client import BedrockLLMClient
from documents.llm_service import llm_service

logger = logging.getLogger(__name__)


@shared_task(bind=True)
def process_document_task(self, document_id):
    """
    Process a PDF document using the existing Buddy PDF Extractor.
    """
    try:
        # Get the document
        document = Document.objects.get(id=document_id)
        document.processing_status = 'processing'
        document.save()
        
        logger.info(f"Starting processing for document {document_id}")
        
        # Initialize the PDF processor
        llm_client = None
        if settings.AWS_ACCESS_KEY_ID and settings.AWS_SECRET_ACCESS_KEY:
            try:
                llm_client = BedrockLLMClient(
                    model_id=settings.BEDROCK_MODEL_ID,
                    region=settings.BEDROCK_REGION
                )
                logger.info("Initialized Bedrock LLM client")
            except Exception as e:
                logger.warning(f"Failed to initialize LLM client: {e}")
        
        processor = PDFProcessor(llm_client=llm_client)
        
        # Process the PDF
        pdf_path = document.file.path
        output_filename = f"{document.id}_processed.json"
        output_path = os.path.join(settings.PROCESSED_JSON_PATH, output_filename)
        
        result = processor.process_pdf(pdf_path, output_path)
        
        # Update document with processing results
        document.processed_json_path = f"processed/{output_filename}"
        document.page_count = result.get('metadata', {}).get('page_count', 0)
        document.document_type = _map_document_type(result.get('metadata', {}).get('document_type', 'other'))
        document.extraction_confidence = result.get('metadata', {}).get('extraction_confidence', 0)
        document.processed_at = timezone.now()
        
        # Extract and save structured data
        _extract_document_structure(document, result)
        
        document.processing_status = 'completed'
        document.save()
        
        logger.info(f"Successfully processed document {document_id}")
        
        return {
            'status': 'success',
            'document_id': str(document_id),
            'page_count': document.page_count,
            'total_sentences': document.total_sentences,
            'total_sections': document.total_sections
        }
        
    except Document.DoesNotExist:
        logger.error(f"Document {document_id} not found")
        return {'status': 'error', 'message': 'Document not found'}
        
    except Exception as e:
        logger.error(f"Error processing document {document_id}: {str(e)}")
        
        # Update document status
        try:
            document = Document.objects.get(id=document_id)
            document.processing_status = 'failed'
            document.save()
        except:
            pass
        
        return {'status': 'error', 'message': str(e)}


def _map_document_type(buddy_type):
    """Map buddy document types to our model choices."""
    mapping = {
        'book': 'book',
        'academic': 'academic',
        'article': 'article',
        'manual': 'manual'
    }
    return mapping.get(buddy_type.lower(), 'other')


def _extract_document_structure(document, result):
    """
    Extract structured data from buddy processing result and save to database.
    """
    logger.info(f"Extracting structure for document {document.id}")

    # Clear existing data for reprocessing
    sections_deleted = DocumentSection.objects.filter(document=document).count()
    sentences_deleted = Sentence.objects.filter(document=document).count()
    images_deleted = DocumentImage.objects.filter(document=document).count()

    DocumentSection.objects.filter(document=document).delete()
    Sentence.objects.filter(document=document).delete()
    DocumentImage.objects.filter(document=document).delete()

    logger.info(f"Cleared existing data: {sections_deleted} sections, {sentences_deleted} sentences, {images_deleted} images")

    # Update document metadata
    metadata = result.get('metadata', {})
    if not document.title and metadata.get('title'):
        document.title = metadata['title']
    if not document.author and metadata.get('author'):
        document.author = metadata['author']

    # Extract sections with LLM enhancement
    sections_data = result.get('sections', [])

    # Use LLM to analyze and improve section structure
    try:
        # Prepare raw text blocks for LLM analysis
        raw_blocks = []
        for section in sections_data[:50]:  # Analyze first 50 sections
            for content in section.get('content', []):
                if content.get('text') or content.get('content'):
                    raw_blocks.append({
                        'text': content.get('text', '') or content.get('content', ''),
                        'font_size': content.get('font_size', 12),
                        'font_name': content.get('font_name', ''),
                        'page': content.get('page', 1),
                        'is_bold': content.get('is_bold', False),
                        'bbox': content.get('bbox', {})
                    })

        if raw_blocks:
            logger.info(f"Analyzing document structure with LLM using {len(raw_blocks)} text blocks")
            llm_analysis = llm_service.analyze_document_structure(raw_blocks)

            # Update document type based on LLM analysis
            if llm_analysis.get('document_type'):
                document.document_type = _map_document_type(llm_analysis['document_type'])
                logger.info(f"LLM identified document type: {llm_analysis['document_type']}")

            # Log LLM insights
            if llm_analysis.get('heading_patterns'):
                logger.info(f"LLM identified {len(llm_analysis['heading_patterns'])} heading patterns")

    except Exception as e:
        logger.warning(f"LLM structure analysis failed: {e}, using original structure")

    document.total_sections = len(sections_data)
    
    section_objects = []
    for i, section_data in enumerate(sections_data):
        section = DocumentSection(
            document=document,
            section_id=section_data.get('id', f"section_{i}"),
            title=section_data.get('title', f"Section {i+1}"),
            level=section_data.get('level', 1),
            page_start=section_data.get('page', 1),
            page_end=section_data.get('page_end', section_data.get('page', 1)),
            order=i
        )
        
        # Add bounding box if available
        bbox = section_data.get('bbox', {})
        if bbox:
            section.bbox_x = bbox.get('x', 0)
            section.bbox_y = bbox.get('y', 0)
            section.bbox_width = bbox.get('width', 0)
            section.bbox_height = bbox.get('height', 0)
        
        section_objects.append(section)
    
    # Bulk create sections
    DocumentSection.objects.bulk_create(section_objects)
    
    # Create a mapping of section IDs to objects for sentence assignment
    section_map = {s.section_id: s for s in DocumentSection.objects.filter(document=document)}
    
    # Extract sentences from content using LLM-enhanced processing
    _extract_sentences_with_llm(document, result, section_map)
    
    # Extract images
    _extract_images(document, result, section_map)
    
    document.save()


def _extract_sentences_with_llm(document, result, section_map):
    """Extract sentences using LLM-enhanced processing for better accuracy."""
    logger.info(f"Extracting sentences with LLM enhancement for document {document.id}")
    logger.info(f"Section map has {len(section_map)} sections")

    sentence_objects = []
    sentence_index = 0
    sections_processed = 0
    paragraphs_found = 0

    try:
        # First, use LLM to extract and clean paragraphs from raw content
        raw_content = []
        for section_data in result.get('sections', []):
            for content_item in section_data.get('content', []):
                raw_content.append({
                    **content_item,
                    'section_id': section_data.get('id', f"section_{len(raw_content)}"),
                    'section_title': section_data.get('title', '')
                })

        # Use LLM to extract clean paragraphs
        logger.info(f"Processing {len(raw_content)} content items with LLM")
        cleaned_paragraphs = llm_service.extract_and_clean_paragraphs(raw_content)
        logger.info(f"LLM extracted {len(cleaned_paragraphs)} cleaned paragraphs")

        # If LLM extraction failed or returned no results, fall back immediately
        if not cleaned_paragraphs:
            logger.info("LLM extraction returned no paragraphs, falling back to basic extraction")
            _extract_sentences(document, result, section_map)
            return

        # Process cleaned paragraphs
        for paragraph_data in cleaned_paragraphs:
            if paragraph_data.get('type') == 'paragraph':
                text = paragraph_data.get('text', '')
                page_num = paragraph_data.get('page', 1)

                # Find the appropriate section
                section_obj = None
                for section in section_map.values():
                    if section.page_start <= page_num <= (section.page_end or section.page_start):
                        section_obj = section
                        break

                if section_obj and text and len(text.strip()) > 0:
                    paragraphs_found += 1

                    # Use LLM for accurate sentence splitting
                    try:
                        sentences = llm_service.segment_sentences(text)
                        if not sentences:  # Fallback if LLM fails
                            sentences = _split_into_sentences(text)
                    except Exception as e:
                        logger.warning(f"LLM sentence segmentation failed: {e}, using fallback")
                        sentences = _split_into_sentences(text)

                    for sentence_text in sentences:
                        if sentence_text.strip():
                            sentence = Sentence(
                                document=document,
                                section=section_obj,
                                text=sentence_text.strip(),
                                sentence_index=sentence_index,
                                page_number=page_num,
                                bbox_x=0,  # LLM-processed content may not have exact bbox
                                bbox_y=0,
                                bbox_width=0,
                                bbox_height=0
                            )
                            sentence_objects.append(sentence)
                            sentence_index += 1

        sections_processed = len(set(p.get('page', 1) for p in cleaned_paragraphs))

    except Exception as e:
        logger.error(f"LLM-enhanced extraction failed: {e}, falling back to basic extraction")
        # Fallback to original method
        _extract_sentences(document, result, section_map)
        return

    # Bulk create sentences
    if sentence_objects:
        Sentence.objects.bulk_create(sentence_objects, batch_size=1000)

    document.total_sentences = len(sentence_objects)
    logger.info(f"LLM processed {sections_processed} sections with {paragraphs_found} paragraphs")
    logger.info(f"Created {len(sentence_objects)} sentences for document {document.id}")


def _extract_sentences(document, result, section_map):
    """Extract sentences from the processed content."""
    logger.info(f"Extracting sentences for document {document.id}")
    logger.info(f"Section map has {len(section_map)} sections")

    sentence_objects = []
    sentence_index = 0
    sections_processed = 0
    paragraphs_found = 0

    # Process all content items to find text content
    for i, section_data in enumerate(result.get('sections', [])):
        section_id = section_data.get('id', f"section_{i}")
        section_obj = section_map.get(section_id)

        if section_obj:
            sections_processed += 1
        
            for content_item in section_data.get('content', []):
                if content_item.get('type') == 'paragraph':
                    paragraphs_found += 1
                    # Try both 'text' and 'content' fields
                    text = content_item.get('text', '') or content_item.get('content', '')

                    if text and len(text.strip()) > 0:
                        # Use LLM for accurate sentence splitting when available
                        try:
                            sentences = llm_service.segment_sentences(text)
                            if not sentences:  # Fallback if LLM fails
                                sentences = _split_into_sentences(text)
                        except Exception as e:
                            logger.warning(f"LLM sentence segmentation failed: {e}, using fallback")
                            sentences = _split_into_sentences(text)

                        for sentence_text in sentences:
                            if sentence_text.strip():
                                sentence = Sentence(
                                    document=document,
                                    section=section_obj,
                                    text=sentence_text.strip(),
                                    sentence_index=sentence_index,
                                    page_number=content_item.get('page', 1),
                                    bbox_x=content_item.get('bbox', {}).get('x', 0),
                                    bbox_y=content_item.get('bbox', {}).get('y', 0),
                                    bbox_width=content_item.get('bbox', {}).get('width', 0),
                                    bbox_height=content_item.get('bbox', {}).get('height', 0)
                                )
                                sentence_objects.append(sentence)
                                sentence_index += 1
    
    # Bulk create sentences
    if sentence_objects:
        Sentence.objects.bulk_create(sentence_objects, batch_size=1000)

    document.total_sentences = len(sentence_objects)
    logger.info(f"Processed {sections_processed} sections with {paragraphs_found} paragraphs")
    logger.info(f"Created {len(sentence_objects)} sentences for document {document.id}")


def _extract_images(document, result, section_map):
    """Extract images from the processed content."""
    logger.info(f"Extracting images for document {document.id}")
    
    image_objects = []
    
    # Extract from media data
    media_data = result.get('media', {})
    images_data = media_data.get('images', [])
    
    for image_data in images_data:
        section_obj = None
        # Try to find the section this image belongs to
        page_num = image_data.get('page', 1)
        for section in section_map.values():
            if section.page_start <= page_num <= (section.page_end or section.page_start):
                section_obj = section
                break
        
        image = DocumentImage(
            document=document,
            section=section_obj,
            image_path=image_data.get('path', ''),
            caption=image_data.get('caption', ''),
            page_number=page_num,
            bbox_x=image_data.get('bbox', {}).get('x', 0),
            bbox_y=image_data.get('bbox', {}).get('y', 0),
            bbox_width=image_data.get('bbox', {}).get('width', 0),
            bbox_height=image_data.get('bbox', {}).get('height', 0)
        )
        image_objects.append(image)
    
    # Bulk create images
    if image_objects:
        DocumentImage.objects.bulk_create(image_objects)
    
    document.total_images = len(image_objects)
    logger.info(f"Created {len(image_objects)} images for document {document.id}")


def _split_into_sentences(text):
    """
    Simple sentence splitting. Could be enhanced with NLTK or spaCy.
    """
    import re
    
    # Simple regex-based sentence splitting
    sentences = re.split(r'[.!?]+\s+', text)
    
    # Clean up and filter
    cleaned_sentences = []
    for sentence in sentences:
        sentence = sentence.strip()
        if len(sentence) > 10:  # Filter out very short fragments
            cleaned_sentences.append(sentence)
    
    return cleaned_sentences
